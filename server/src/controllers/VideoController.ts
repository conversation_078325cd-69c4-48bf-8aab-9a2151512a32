import { Request, Response } from "express";
import { spawn } from "child_process";
import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { ProgressTracker } from "../services/ProgressTracker";
import { TaskQueue, TaskStatus } from "../services/TaskQueue";
import logger from "../utils/logger";
import fs from "fs";
import path from "path";
import os from "os";

// Request timeout in milliseconds (30 minutes)
const REQUEST_TIMEOUT = 30 * 60 * 1000;

export class VideoController {
  private progressTracker: ProgressTracker;
  private taskQueue: TaskQueue;

  constructor(progressTracker: ProgressTracker) {
    this.progressTracker = progressTracker;
    this.taskQueue = new TaskQueue();

    // 设置任务完成回调
    this.taskQueue.setTaskCompleteCallback((taskId, success) => {
      logger.info(`任务队列回调: 任务 ${taskId} ${success ? "成功" : "失败"}`);
      // 这里可以添加额外的任务完成处理逻辑
    });

    // 设置定时清理任务
    setInterval(() => {
      this.taskQueue.cleanupOldTasks(24); // 清理24小时前的任务信息
    }, 3600000); // 每小时执行一次
  }

  async generateVideo(req: Request, res: Response): Promise<void> {
    const taskId = Date.now().toString();

    try {
      // 初始化任务进度
      this.progressTracker.initializeTask(taskId);

      // 获取队列位置
      const queuePosition = this.taskQueue.enqueue(taskId, async () => {
        // 这个函数会在任务从队列中取出执行时调用
        await this.executeVideoGenerationTask(taskId, req.body);
      });

      // 返回任务ID和队列位置
      res.json({
        taskId,
        queuePosition,
        message:
          queuePosition > 1
            ? `任务已加入队列，当前队列位置: ${queuePosition}`
            : "任务已开始处理",
      });
    } catch (error) {
      logger.error(`创建任务 ${taskId} 失败:`, error);

      // 更新任务状态为失败
      this.markTaskFailed(
        taskId,
        error instanceof Error ? error.message : "Unknown error"
      );

      if (!res.headersSent) {
        res.status(500).json({
          error: "Internal server error",
          details: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }
  }

  /**
   * 执行视频生成任务
   * 这个方法会在任务从队列中取出时被调用
   * @param taskId 任务ID
   * @param requestBody 请求体
   */
  private async executeVideoGenerationTask(
    taskId: string,
    requestBody: any
  ): Promise<void> {
    let ffmpeg: ReturnType<typeof spawn> | null = null;
    let timeout: NodeJS.Timeout | null = null;
    let commandGenerator = new FFmpegCommandGenerator();
    let outputFilePath: string | undefined;

    try {
      logger.info(`开始执行队列中的任务 ${taskId}`);

      // 更新进度状态为处理中
      const progress = this.progressTracker.getProgress(taskId);
      if (progress) {
        progress.stage = "command_generation";
      }

      // 生成FFmpeg命令
      const ffmpegCommand = await commandGenerator.generateCommand(
        requestBody,
        taskId,
        this.progressTracker
      );

      // 从ffmpeg命令中提取输出文件路径
      outputFilePath = this.extractOutputFilePath(ffmpegCommand);
      if (outputFilePath) {
        logger.info(`任务 ${taskId} 的输出文件路径: ${outputFilePath}`);
      }

      // 解析命令并启动FFmpeg进程
      const [cmd, ...args] = this.parseFFmpegCommand(ffmpegCommand);
      ffmpeg = spawn(cmd, args);

      // 跟踪FFmpeg进度
      this.progressTracker.trackFFmpegProgress(taskId, ffmpeg);

      // 设置超时
      timeout = this.setupTimeout(taskId, ffmpeg);

      // 注册活动进程
      if (ffmpeg && timeout) {
        this.registerActiveProcess(
          taskId,
          ffmpeg,
          timeout,
          commandGenerator,
          outputFilePath
        );
      }

      // 等待FFmpeg进程完成
      return new Promise<void>((resolve, reject) => {
        ffmpeg?.on("error", (error) => {
          logger.error(`FFmpeg process error for task ${taskId}:`, error);
          this.cleanup(ffmpeg, timeout);
          commandGenerator.destroy();
          this.unregisterActiveProcess(taskId);
          reject(error);
        });

        ffmpeg?.on("close", (code) => {
          this.cleanup(ffmpeg, timeout);
          commandGenerator.destroy();
          this.unregisterActiveProcess(taskId);

          if (code !== 0) {
            const error = new Error(`FFmpeg process exited with code ${code}`);
            logger.error(`任务 ${taskId} 失败: ${error.message}`);
            reject(error);
          } else {
            logger.info(`任务 ${taskId} 成功完成`);
            resolve();
          }
        });
      });
    } catch (error) {
      // 清理资源
      this.cleanup(ffmpeg, timeout);
      commandGenerator.destroy();

      // 如果进程已注册，取消注册
      if (ffmpeg && timeout) {
        this.unregisterActiveProcess(taskId);
      }

      logger.error(`任务 ${taskId} 执行错误:`, error);

      // 更新任务状态为失败
      this.markTaskFailed(
        taskId,
        error instanceof Error ? error.message : "Unknown error"
      );

      // 重新抛出错误，让任务队列知道任务失败
      throw error;
    }
  }

  private parseFFmpegCommand(command: string): string[] {
    return (
      command
        .match(/(?:[^\s"]+|"[^"]*")+/g)
        ?.map((arg) => arg.replace(/"/g, "")) || []
    );
  }

  private setupTimeout(
    taskId: string,
    ffmpeg: ReturnType<typeof spawn>
  ): NodeJS.Timeout {
    return setTimeout(() => {
      ffmpeg.kill();
      logger.error(`Task ${taskId} timed out after ${REQUEST_TIMEOUT}ms`);
    }, REQUEST_TIMEOUT);
  }

  private cleanup(
    ffmpeg: ReturnType<typeof spawn> | null,
    timeout: NodeJS.Timeout | null
  ): void {
    if (timeout) {
      clearTimeout(timeout);
    }
    if (ffmpeg) {
      ffmpeg.kill();
    }
  }

  getProgress(req: Request, res: Response): void {
    const taskId = req.params.taskId;
    const progress = this.progressTracker.getProgress(taskId);

    if (!progress) {
      // 检查任务是否在队列中但尚未开始处理
      const taskInfo = this.taskQueue.getTaskInfo(taskId);
      if (taskInfo) {
        // 任务在队列中，返回队列信息
        const queuePosition = this.taskQueue.getQueuePosition(taskId);
        res.json({
          status: "queued",
          progress: 0,
          stage: "queued",
          queuePosition,
          queuedAt: taskInfo.queuedAt,
          message:
            queuePosition > 1
              ? `任务在队列中，当前位置: ${queuePosition}`
              : "任务即将开始处理",
        });
        return;
      }

      res.status(404).json({ error: "Task not found" });
      return;
    }

    // 获取队列位置
    const queuePosition = this.taskQueue.getQueuePosition(taskId);

    // 合并进度信息和队列信息
    const response = {
      ...progress,
      queuePosition: queuePosition > 0 ? queuePosition : undefined,
    };

    res.json(response);
  }

  downloadVideo(req: Request, res: Response): void {
    const taskId = req.params.taskId;
    const progress = this.progressTracker.getProgress(taskId);

    if (!progress || progress.status !== "completed") {
      res.status(404).json({ error: "视频不存在或尚未生成完成" });
      return;
    }

    // 检查不同可能的文件名和目录
    const outputDirs = ["./output", "./server/output", ".", "./server"];
    const possibleExtensions = ["mp4", "mov", "webm", "gif"];
    let videoPath = null;

    // 记录检查的路径，用于调试
    const checkedPaths: string[] = [];

    // 尝试查找视频文件
    for (const dir of outputDirs) {
      if (!fs.existsSync(dir)) {
        logger.debug(`目录不存在: ${dir}`);
        continue;
      }

      logger.info(`在目录 ${dir} 中查找视频文件`);

      // 检查特定命名模式
      for (const ext of possibleExtensions) {
        const possiblePaths = [
          `${dir}/${taskId}.${ext}`, // {taskId}.mp4
          `${dir}/output_${taskId}.${ext}`, // output_{taskId}.mp4
          `${dir}/video-${taskId}.${ext}`, // video-{taskId}.mp4
        ];

        for (const path of possiblePaths) {
          checkedPaths.push(path);
          if (fs.existsSync(path)) {
            videoPath = path;
            logger.info(`找到精确匹配的视频文件: ${path}`);
            break;
          }
        }
        if (videoPath) break;
      }
      if (videoPath) break;

      // 检查目录中是否有最近创建的输出文件
      try {
        const files = fs.readdirSync(dir);
        logger.info(`目录 ${dir} 中有 ${files.length} 个文件`);

        // 找到与任务ID时间匹配的最近文件
        const taskTime = parseInt(taskId);
        const timeWindow = 5 * 60 * 1000; // 5分钟时间窗口

        // 获取所有视频文件并按创建时间排序
        const recentFiles = files
          .filter((file) =>
            possibleExtensions.some((ext) => file.endsWith(`.${ext}`))
          )
          .map((file) => {
            const fullPath = `${dir}/${file}`;
            try {
              const stats = fs.statSync(fullPath);
              return {
                path: fullPath,
                time: stats.ctime.getTime(),
                timeDistance: Math.abs(stats.ctime.getTime() - taskTime),
              };
            } catch (e) {
              return null;
            }
          })
          .filter((item) => item !== null && item.timeDistance < timeWindow)
          .sort((a, b) => (a?.timeDistance || 0) - (b?.timeDistance || 0));

        if (recentFiles.length > 0 && recentFiles[0]) {
          videoPath = recentFiles[0].path;
          logger.info(
            `找到时间匹配的视频文件: ${videoPath} (创建于任务时间差 ${recentFiles[0].timeDistance}ms)`
          );
          break;
        }

        // 如果没有找到时间匹配的文件，尝试按名称模式查找
        if (!videoPath) {
          const outputFiles = files
            .filter(
              (file) =>
                file.startsWith("output_") &&
                possibleExtensions.some((ext) => file.endsWith(`.${ext}`))
            )
            .sort((a, b) => {
              try {
                const statsA = fs.statSync(`${dir}/${a}`);
                const statsB = fs.statSync(`${dir}/${b}`);
                return statsB.ctime.getTime() - statsA.ctime.getTime(); // 按时间降序排列
              } catch (e) {
                return 0;
              }
            });

          if (outputFiles.length > 0) {
            videoPath = `${dir}/${outputFiles[0]}`;
            logger.info(`找到最近创建的output_前缀视频文件: ${videoPath}`);
            break;
          }
        }
      } catch (error) {
        logger.error(`扫描目录失败 ${dir}: ${error}`);
      }
    }

    // 如果找不到文件
    if (!videoPath) {
      logger.error(`未找到视频文件，任务ID: ${taskId}，检查了以下路径:`);
      checkedPaths.forEach((path) => logger.debug(`- ${path}`));
      res.status(404).json({ error: "视频文件未找到" });
      return;
    }

    logger.info(`找到视频文件: ${videoPath}`);

    // 设置文件名和内容类型
    const fileExt = videoPath.split(".").pop() || "mp4";
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="video-${taskId}.${fileExt}"`
    );
    res.setHeader("Content-Type", `video/${fileExt}`);

    // 发送文件（使用绝对路径，避免路径解析问题）
    const absolutePath = path.resolve(videoPath);
    logger.info(`发送文件的绝对路径: ${absolutePath}`);
    res.sendFile(absolutePath);
  }

  private markTaskFailed(taskId: string, errorMessage: string): void {
    const currentProgress = this.progressTracker.getProgress(taskId);
    if (currentProgress) {
      // If we have some progress info for this task, update it to failed
      // Re-initialize it with the same taskId but with failed status
      this.progressTracker.initializeTask(taskId);
      const progress = this.progressTracker.getProgress(taskId);
      if (progress) {
        // Check if this is a cancellation
        const isCancelled = errorMessage === "任务已取消";

        // Update with more specific failed information
        progress.status = isCancelled ? "cancelled" : "failed";
        progress.stage = isCancelled ? "cancelled" : "failed";
        progress.error = errorMessage;
        progress.progress = 0;
        logger.info(
          `Marked task ${taskId} as ${progress.status}: ${errorMessage}`
        );
      }
    }
  }

  // Map to store active ffmpeg processes by taskId
  private activeProcesses = new Map<
    string,
    {
      ffmpeg: ReturnType<typeof spawn>;
      timeout: NodeJS.Timeout;
      commandGenerator: FFmpegCommandGenerator;
      outputFilePath?: string;
    }
  >();

  // Register active process
  private registerActiveProcess(
    taskId: string,
    ffmpeg: ReturnType<typeof spawn>,
    timeout: NodeJS.Timeout,
    commandGenerator: FFmpegCommandGenerator,
    outputFilePath?: string
  ): void {
    this.activeProcesses.set(taskId, {
      ffmpeg,
      timeout,
      commandGenerator,
      outputFilePath,
    });
  }

  // Unregister active process
  private unregisterActiveProcess(taskId: string): void {
    this.activeProcesses.delete(taskId);
  }

  // Cancel an in-progress video generation task
  cancelVideo(req: Request, res: Response): void {
    const taskId = req.params.taskId;
    logger.info(`正在尝试取消任务 ${taskId}`);

    // 首先检查任务是否在队列中
    const queuePosition = this.taskQueue.getQueuePosition(taskId);

    if (queuePosition > 0) {
      // 任务在队列中但尚未开始执行
      if (queuePosition > 1) {
        // 从队列中移除任务
        const removed = this.taskQueue.dequeue(taskId);
        if (removed) {
          // 将任务标记为取消状态
          this.markTaskFailed(taskId, "任务已取消");
          logger.info(`已从队列中移除任务 ${taskId}`);
          res.json({ success: true, message: "任务已从队列中移除" });
          return;
        }
      }
    }

    // 检查任务是否正在执行
    const activeProcess = this.activeProcesses.get(taskId);
    if (!activeProcess) {
      // 检查任务是否存在
      const taskInfo = this.taskQueue.getTaskInfo(taskId);
      const progress = this.progressTracker.getProgress(taskId);

      if (!taskInfo && !progress) {
        logger.warn(`无法取消任务 ${taskId}: 未找到任务`);
        res.status(404).json({ error: "Task not found" });
        return;
      }

      if (
        progress &&
        (progress.status === "completed" || progress.status === "failed")
      ) {
        logger.warn(`无法取消任务 ${taskId}: 任务已完成或失败`);
        res.status(400).json({ error: "Task already completed or failed" });
        return;
      }

      logger.warn(`无法取消任务 ${taskId}: 未找到活动进程`);
      res.status(404).json({ error: "Active process not found" });
      return;
    }

    // 清理资源（停止ffmpeg进程）
    this.cleanup(activeProcess.ffmpeg, activeProcess.timeout);
    activeProcess.commandGenerator.destroy();

    // 记录任务的原始输出文件路径
    if (activeProcess.outputFilePath) {
      logger.info(
        `任务 ${taskId} 的原始输出文件路径: ${activeProcess.outputFilePath}`
      );
    } else {
      logger.warn(`任务 ${taskId} 没有记录输出文件路径`);
    }

    // 尝试删除生成的输出文件
    this.cleanupOutputFile(taskId);

    // 将任务标记为取消状态
    this.markTaskFailed(taskId, "任务已取消");

    // 从活动进程中移除
    this.unregisterActiveProcess(taskId);

    logger.info(`任务 ${taskId} 已成功取消`);
    res.json({ success: true, message: "任务已成功取消" });
  }

  // Helper method to clean up output video files
  private cleanupOutputFile(taskId: string): void {
    // 检查活动进程中记录的输出文件路径（这是最准确的）
    const activeProcess = this.activeProcesses.get(taskId);
    let exactOutputPath = activeProcess?.outputFilePath;

    if (exactOutputPath) {
      logger.info(`找到任务 ${taskId} 的精确输出文件路径: ${exactOutputPath}`);
      if (fs.existsSync(exactOutputPath)) {
        try {
          fs.unlinkSync(exactOutputPath);
          logger.info(`成功删除任务的输出文件: ${exactOutputPath}`);
          return; // 找到并删除了确切的文件，可以直接返回
        } catch (error) {
          logger.error(`删除确切输出文件失败 ${exactOutputPath}: ${error}`);
          // 如果删除失败，继续尝试其他方法
        }
      } else {
        logger.warn(`确切的输出文件不存在: ${exactOutputPath}`);
      }
    }

    // 如果没有确切的路径或删除失败，使用基于任务ID的匹配
    // 可能的输出目录
    const outputDirs = ["./output", "./server/output", ".", "./server"];
    const possibleExtensions = ["mp4", "mov", "webm", "gif"];

    // 记录删除的文件
    let filesDeleted = false;

    // 记录所有检查的文件路径，方便调试
    const checkedPaths: string[] = [];

    // 只使用精确的任务ID匹配，不使用模糊时间匹配
    for (const dir of outputDirs) {
      if (!fs.existsSync(dir)) {
        continue;
      }

      // 检查确切的命名模式
      for (const ext of possibleExtensions) {
        // 只检查确切包含任务ID的文件名
        const possiblePaths = [
          `${dir}/${taskId}.${ext}`, // {taskId}.mp4
          `${dir}/output_${taskId}.${ext}`, // output_{taskId}.mp4
          `${dir}/video-${taskId}.${ext}`, // video-{taskId}.mp4
        ];

        for (const path of possiblePaths) {
          checkedPaths.push(path);
          if (fs.existsSync(path)) {
            try {
              fs.unlinkSync(path);
              logger.info(`成功删除包含任务ID的文件: ${path}`);
              filesDeleted = true;
            } catch (error) {
              logger.error(`删除视频文件失败 ${path}: ${error}`);
            }
          }
        }
      }

      // 如果我们通过确切的任务ID已经找到并删除了文件，就不需要继续检查了
      if (filesDeleted) {
        break;
      }

      // 如果上面没找到，检查是否有文件名中包含完整任务ID的文件
      try {
        const files = fs.readdirSync(dir);

        for (const file of files) {
          // 只删除文件名中确实包含完整任务ID的文件，不使用模糊匹配
          if (
            file.includes(taskId) &&
            possibleExtensions.some((ext) => file.endsWith(`.${ext}`))
          ) {
            const filePath = `${dir}/${file}`;
            try {
              fs.unlinkSync(filePath);
              logger.info(`删除包含完整任务ID的文件: ${filePath}`);
              filesDeleted = true;
            } catch (error) {
              logger.error(`删除文件失败 ${filePath}: ${error}`);
            }
          }
        }
      } catch (error) {
        logger.error(`扫描目录失败 ${dir}: ${error}`);
      }
    }

    if (!filesDeleted) {
      logger.warn(`未找到任何与任务 ${taskId} 关联的视频文件`);
      if (checkedPaths.length > 0) {
        logger.debug(`检查了以下路径:`);
        checkedPaths.forEach((path) => logger.debug(`- ${path}`));
      }
    }
  }

  // 从ffmpeg命令中提取输出文件路径
  private extractOutputFilePath(command: string): string | undefined {
    // 命令的最后一个参数通常是输出文件
    const parts = this.parseFFmpegCommand(command);
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1];
      // 检查是否是输出文件（不是以"-"开头的参数）
      if (!lastPart.startsWith("-")) {
        const outputPath = lastPart;

        // 检查路径是否有效
        try {
          // 如果输出路径不存在，需要检查目录是否存在
          if (outputPath.includes("/")) {
            const dir = outputPath.substring(0, outputPath.lastIndexOf("/"));
            if (!fs.existsSync(dir)) {
              logger.warn(`输出目录不存在: ${dir}，将使用绝对路径`);
              return path.resolve(outputPath);
            }
          }

          // 记录并返回提取到的路径
          logger.info(`提取到的输出文件路径: ${outputPath}`);
          return outputPath;
        } catch (error) {
          logger.error(`检查输出路径时出错: ${error}`);
          return outputPath; // 尽管出错，仍然返回找到的路径
        }
      }
    }
    return undefined;
  }

  /**
   * 清理所有活动进程
   * 在服务关闭时调用，确保所有FFmpeg进程被正确终止
   */
  public cleanupAllProcesses(): void {
    // 清理活动进程
    logger.info(`正在清理所有活动进程，数量: ${this.activeProcesses.size}`);
    for (const [taskId, process] of this.activeProcesses.entries()) {
      try {
        this.cleanup(process.ffmpeg, process.timeout);
        process.commandGenerator.destroy();
        logger.info(`成功清理任务 ${taskId} 的资源`);

        // 更新任务状态
        this.markTaskFailed(taskId, "服务关闭，任务被终止");
      } catch (error) {
        logger.error(`清理任务 ${taskId} 资源失败: ${error}`);
      }
    }
    this.activeProcesses.clear();

    // 获取所有队列中的任务
    const allTasks = this.taskQueue.getAllTaskInfo();
    const queuedTasks = allTasks.filter(
      (task) =>
        task.status === TaskStatus.QUEUED || task.status === TaskStatus.RUNNING
    );

    if (queuedTasks.length > 0) {
      logger.info(`正在清理队列中的 ${queuedTasks.length} 个任务`);

      // 更新所有队列中任务的状态
      for (const task of queuedTasks) {
        this.markTaskFailed(task.taskId, "服务关闭，队列中的任务被取消");
      }
    }
  }

  /**
   * 清理旧的输出文件
   * 删除超过指定时间（默认7天）的输出文件
   * @param maxAgeDays 文件最大保留天数，默认7天
   */
  public cleanupOldOutputFiles(maxAgeDays: number = 7): void {
    const outputDirs = ["./output", "./server/output"];
    const now = Date.now();
    const maxAgeMs = maxAgeDays * 24 * 3600000; // 转换为毫秒
    const cutoffTime = now - maxAgeMs;

    logger.info(`开始清理超过 ${maxAgeDays} 天的旧输出文件`);

    for (const dir of outputDirs) {
      if (!fs.existsSync(dir)) {
        logger.debug(`目录不存在，跳过: ${dir}`);
        continue;
      }

      try {
        const files = fs.readdirSync(dir);
        let cleanedCount = 0;

        for (const file of files) {
          // 只处理视频文件
          if (file.startsWith("output_") || file.includes("video-")) {
            const filePath = path.join(dir, file);
            try {
              const stats = fs.statSync(filePath);
              if (stats.ctime.getTime() < cutoffTime) {
                fs.unlinkSync(filePath);
                cleanedCount++;
                logger.info(`清理过期输出文件: ${filePath}`);
              }
            } catch (error) {
              logger.error(`清理输出文件失败 ${filePath}: ${error}`);
            }
          }
        }

        if (cleanedCount > 0) {
          logger.info(`在目录 ${dir} 中清理了 ${cleanedCount} 个过期文件`);
        } else {
          logger.info(`目录 ${dir} 中没有过期文件需要清理`);
        }
      } catch (error) {
        logger.error(`扫描输出目录失败 ${dir}: ${error}`);
      }
    }
  }

  /**
   * 清理临时文件
   * 删除系统临时目录中超过指定时间（默认1小时）的ffmpeg_前缀文件
   * @param maxAgeHours 文件最大保留小时数，默认1小时
   */
  public cleanupTempFiles(maxAgeHours: number = 1): void {
    const tempDir = os.tmpdir();
    const now = Date.now();
    const maxAgeMs = maxAgeHours * 3600000; // 转换为毫秒
    const cutoffTime = now - maxAgeMs;

    logger.info(`开始清理超过 ${maxAgeHours} 小时的临时文件`);

    try {
      const files = fs.readdirSync(tempDir);
      let cleanedCount = 0;

      for (const file of files) {
        if (file.startsWith("ffmpeg_")) {
          const filePath = path.join(tempDir, file);
          try {
            const stats = fs.statSync(filePath);
            if (stats.ctime.getTime() < cutoffTime) {
              fs.unlinkSync(filePath);
              cleanedCount++;
              logger.debug(`清理过期临时文件: ${filePath}`);
            }
          } catch (error) {
            logger.error(`清理临时文件失败 ${filePath}: ${error}`);
          }
        }
      }

      if (cleanedCount > 0) {
        logger.info(`共清理了 ${cleanedCount} 个过期临时文件`);
      } else {
        logger.info(`没有过期临时文件需要清理`);
      }
    } catch (error) {
      logger.error(`扫描临时目录失败: ${error}`);
    }
  }
}
