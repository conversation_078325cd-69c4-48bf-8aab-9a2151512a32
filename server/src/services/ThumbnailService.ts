import { S3Client, GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { createS3Client, getS3Config, S3_PATHS } from "../config/s3Config";
import { MediaRepository } from "../repositories/MediaRepository";
import logger from "../utils/logger";
import { spawn } from "child_process";
import { createWriteStream, createReadStream, unlinkSync, existsSync } from "fs";
import { tmpdir } from "os";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";
import sharp from "sharp";

/**
 * 缩略图尺寸配置
 */
export interface ThumbnailSize {
  name: string;
  width: number;
  height: number;
  quality?: number;
}

/**
 * 默认缩略图尺寸
 */
export const DEFAULT_THUMBNAIL_SIZES: ThumbnailSize[] = [
  { name: "small", width: 150, height: 150, quality: 80 },
  { name: "medium", width: 300, height: 300, quality: 85 },
  { name: "large", width: 600, height: 600, quality: 90 },
];

/**
 * 缩略图生成任务状态
 */
export interface ThumbnailTask {
  taskId: string;
  mediaId: string;
  fileKey: string;
  fileType: string;
  sizes: string[];
  status: "pending" | "processing" | "completed" | "failed";
  createdAt: Date;
  completedAt?: Date;
  error?: string;
  thumbnailUrls?: { [size: string]: string };
}

/**
 * 缩略图生成服务
 */
export class ThumbnailService {
  private s3Client: S3Client;
  private config: ReturnType<typeof getS3Config>;
  private mediaRepository: MediaRepository;
  private tasks: Map<string, ThumbnailTask> = new Map();

  constructor() {
    this.s3Client = createS3Client();
    this.config = getS3Config();
    this.mediaRepository = new MediaRepository();
  }

  /**
   * 创建缩略图生成任务
   */
  async createThumbnailTask(
    mediaId: string,
    fileKey: string,
    fileType: string,
    sizes: string[] = ["medium"]
  ): Promise<string> {
    const taskId = uuidv4();
    const task: ThumbnailTask = {
      taskId,
      mediaId,
      fileKey,
      fileType,
      sizes,
      status: "pending",
      createdAt: new Date(),
    };

    this.tasks.set(taskId, task);
    logger.info(`Created thumbnail task: ${taskId} for media: ${mediaId}`);

    // 异步处理任务
    this.processThumbnailTask(taskId).catch((error) => {
      logger.error(`Thumbnail task ${taskId} failed:`, error);
      this.updateTaskStatus(taskId, "failed", error.message);
    });

    return taskId;
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): ThumbnailTask | null {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 处理缩略图生成任务
   */
  private async processThumbnailTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    this.updateTaskStatus(taskId, "processing");

    try {
      const thumbnailUrls: { [size: string]: string } = {};

      if (task.fileType.startsWith("image/")) {
        // 处理图片缩略图
        for (const sizeName of task.sizes) {
          const thumbnailUrl = await this.generateImageThumbnail(task.fileKey, sizeName);
          thumbnailUrls[sizeName] = thumbnailUrl;
        }
      } else if (task.fileType.startsWith("video/")) {
        // 处理视频缩略图
        for (const sizeName of task.sizes) {
          const thumbnailUrl = await this.generateVideoThumbnail(task.fileKey, sizeName);
          thumbnailUrls[sizeName] = thumbnailUrl;
        }
      } else {
        throw new Error(`Unsupported file type for thumbnail generation: ${task.fileType}`);
      }

      // 更新媒体元数据
      await this.mediaRepository.updateMedia(task.mediaId, {
        thumbnailUrl: thumbnailUrls.medium || Object.values(thumbnailUrls)[0],
      });

      // 更新任务状态
      task.thumbnailUrls = thumbnailUrls;
      this.updateTaskStatus(taskId, "completed");

      logger.info(`Thumbnail task completed: ${taskId}`);
    } catch (error) {
      logger.error(`Thumbnail generation failed for task ${taskId}:`, error);
      this.updateTaskStatus(taskId, "failed", error instanceof Error ? error.message : "Unknown error");
    }
  }

  /**
   * 生成图片缩略图
   */
  private async generateImageThumbnail(fileKey: string, sizeName: string): Promise<string> {
    const sizeConfig = DEFAULT_THUMBNAIL_SIZES.find(s => s.name === sizeName);
    if (!sizeConfig) {
      throw new Error(`Unknown thumbnail size: ${sizeName}`);
    }

    // 从S3下载原始图片
    const tempInputPath = join(tmpdir(), `thumbnail-input-${uuidv4()}.tmp`);
    const tempOutputPath = join(tmpdir(), `thumbnail-output-${uuidv4()}.jpg`);

    try {
      await this.downloadFromS3(fileKey, tempInputPath);

      // 使用Sharp生成缩略图
      await sharp(tempInputPath)
        .resize(sizeConfig.width, sizeConfig.height, {
          fit: "cover",
          position: "center",
        })
        .jpeg({ quality: sizeConfig.quality || 85 })
        .toFile(tempOutputPath);

      // 上传缩略图到S3
      const thumbnailKey = S3_PATHS.getThumbnailPath(fileKey, sizeName);
      const thumbnailUrl = await this.uploadToS3(tempOutputPath, thumbnailKey, "image/jpeg");

      return thumbnailUrl;
    } finally {
      // 清理临时文件
      this.cleanupTempFile(tempInputPath);
      this.cleanupTempFile(tempOutputPath);
    }
  }

  /**
   * 生成视频缩略图
   */
  private async generateVideoThumbnail(fileKey: string, sizeName: string): Promise<string> {
    const sizeConfig = DEFAULT_THUMBNAIL_SIZES.find(s => s.name === sizeName);
    if (!sizeConfig) {
      throw new Error(`Unknown thumbnail size: ${sizeName}`);
    }

    // 从S3下载原始视频
    const tempInputPath = join(tmpdir(), `video-input-${uuidv4()}.tmp`);
    const tempOutputPath = join(tmpdir(), `video-thumbnail-${uuidv4()}.jpg`);

    try {
      await this.downloadFromS3(fileKey, tempInputPath);

      // 使用FFmpeg提取视频帧
      await this.extractVideoFrame(tempInputPath, tempOutputPath, sizeConfig);

      // 上传缩略图到S3
      const thumbnailKey = S3_PATHS.getThumbnailPath(fileKey, sizeName);
      const thumbnailUrl = await this.uploadToS3(tempOutputPath, thumbnailKey, "image/jpeg");

      return thumbnailUrl;
    } finally {
      // 清理临时文件
      this.cleanupTempFile(tempInputPath);
      this.cleanupTempFile(tempOutputPath);
    }
  }

  /**
   * 使用FFmpeg提取视频帧
   */
  private async extractVideoFrame(
    inputPath: string,
    outputPath: string,
    sizeConfig: ThumbnailSize
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const ffmpeg = spawn("ffmpeg", [
        "-i", inputPath,
        "-ss", "00:00:01", // 提取第1秒的帧
        "-vframes", "1", // 只提取一帧
        "-vf", `scale=${sizeConfig.width}:${sizeConfig.height}:force_original_aspect_ratio=increase,crop=${sizeConfig.width}:${sizeConfig.height}`,
        "-q:v", "2", // 高质量
        "-y", // 覆盖输出文件
        outputPath,
      ]);

      ffmpeg.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`FFmpeg exited with code ${code}`));
        }
      });

      ffmpeg.on("error", (error) => {
        reject(error);
      });
    });
  }

  /**
   * 从S3下载文件
   */
  private async downloadFromS3(fileKey: string, localPath: string): Promise<void> {
    const command = new GetObjectCommand({
      Bucket: this.config.bucketName,
      Key: fileKey,
    });

    const response = await this.s3Client.send(command);
    if (!response.Body) {
      throw new Error("No body in S3 response");
    }

    const writeStream = createWriteStream(localPath);
    
    return new Promise((resolve, reject) => {
      if (response.Body instanceof ReadableStream) {
        const reader = response.Body.getReader();
        
        const pump = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;
              writeStream.write(Buffer.from(value));
            }
            writeStream.end();
            resolve();
          } catch (error) {
            writeStream.destroy();
            reject(error);
          }
        };
        
        pump();
      } else {
        // Node.js stream
        (response.Body as any).pipe(writeStream);
        writeStream.on("finish", resolve);
        writeStream.on("error", reject);
      }
    });
  }

  /**
   * 上传文件到S3
   */
  private async uploadToS3(localPath: string, s3Key: string, contentType: string): Promise<string> {
    const fileStream = createReadStream(localPath);
    
    const command = new PutObjectCommand({
      Bucket: this.config.bucketName,
      Key: s3Key,
      Body: fileStream,
      ContentType: contentType,
    });

    await this.s3Client.send(command);
    
    return `https://${this.config.bucketName}.s3.${this.config.region}.amazonaws.com/${s3Key}`;
  }

  /**
   * 清理临时文件
   */
  private cleanupTempFile(filePath: string): void {
    try {
      if (existsSync(filePath)) {
        unlinkSync(filePath);
      }
    } catch (error) {
      logger.warn(`Failed to cleanup temp file ${filePath}:`, error);
    }
  }

  /**
   * 更新任务状态
   */
  private updateTaskStatus(taskId: string, status: ThumbnailTask["status"], error?: string): void {
    const task = this.tasks.get(taskId);
    if (task) {
      task.status = status;
      if (status === "completed" || status === "failed") {
        task.completedAt = new Date();
      }
      if (error) {
        task.error = error;
      }
    }
  }

  /**
   * 清理过期任务
   */
  cleanupExpiredTasks(maxAgeHours: number = 24): void {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.createdAt < cutoffTime && (task.status === "completed" || task.status === "failed")) {
        this.tasks.delete(taskId);
        logger.info(`Cleaned up expired thumbnail task: ${taskId}`);
      }
    }
  }
}
