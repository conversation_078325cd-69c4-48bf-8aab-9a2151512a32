# Agent Configuration

## Build/Test Commands
- **Frontend dev**: `cd frontend && npm start` (http://localhost:3000)
- **Server dev**: `cd server && npm run dev`
- **Frontend build**: `cd frontend && npm run build`
- **Server build**: `cd server && npm run build`
- **Frontend test**: `cd frontend && npm test`
- **Server test**: `cd server && npm test`
- **Server lint**: `cd server && npm run lint`

## Code Style Guidelines
- **Language**: TypeScript for both frontend and backend
- **Frontend**: React with MobX state management, Material-UI components
- **Components**: PascalCase naming (e.g., `VideoEditor.tsx`)
- **Files**: `ComponentName.tsx`, `utils.ts`, `types.ts`, `ComponentName.test.tsx`
- **Imports**: Use absolute imports with `@/` prefix where configured
- **Props**: Use TypeScript interfaces for component props
- **State**: MobX for global state, React hooks for local state
- **Styling**: CSS Modules or Material-UI sx prop
- **Error handling**: Try-catch for async operations, unified error middleware

## Project Structure
- `frontend/src/`: React app with components/, editor/, store/, utils/, services/
- `server/src/`: Node.js with controllers/, ffmpeg/, services/, utils/
- Cursor rules in `.cursor/rules/` define detailed coding standards
- Follow existing patterns in MobX stores and Fabric.js canvas components
