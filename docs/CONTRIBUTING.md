# 贡献指南

## 欢迎贡献

感谢您对 Fabric 视频编辑器项目的关注！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能
- 🧪 编写测试用例

## 开始之前

### 1. 了解项目

在开始贡献之前，请：
- 阅读 [README.md](../README.md) 了解项目概况
- 查看 [API 文档](./API.md) 了解接口设计
- 浏览 [前端开发指南](./FRONTEND_GUIDE.md) 了解架构

### 2. 环境准备

确保您的开发环境满足以下要求：
- Node.js 18.x 或更高版本
- npm 8.x 或更高版本
- Git 2.x 或更高版本
- FFmpeg 4.4 或更高版本

### 3. Fork 和克隆项目

```bash
# Fork 项目到您的 GitHub 账户
# 然后克隆到本地
git clone https://github.com/YOUR_USERNAME/online_video.git
cd online_video

# 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/online_video.git
```

## 开发流程

### 1. 创建分支

```bash
# 从 main 分支创建新的功能分支
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name

# 或者修复 bug
git checkout -b fix/bug-description
```

### 2. 分支命名规范

- **功能开发**: `feature/feature-name`
- **Bug 修复**: `fix/bug-description`
- **文档更新**: `docs/update-description`
- **重构代码**: `refactor/component-name`
- **性能优化**: `perf/optimization-description`
- **测试相关**: `test/test-description`

### 3. 提交规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 格式
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型

- **feat**: 新功能
- **fix**: Bug 修复
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 重构代码
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 示例

```bash
feat(timeline): add drag and drop functionality

- Implement drag and drop for timeline elements
- Add visual feedback during dragging
- Update element positions in real-time

Closes #123
```

### 4. 代码规范

#### 前端代码规范

```typescript
// 使用 TypeScript 严格模式
// 组件命名使用 PascalCase
const VideoEditor: React.FC<VideoEditorProps> = ({ }) => {
  // 使用 camelCase 命名变量和函数
  const [isPlaying, setIsPlaying] = useState(false);
  
  // 使用 observer 包装 MobX 组件
  return observer(() => (
    <div className="video-editor">
      {/* JSX 内容 */}
    </div>
  ));
};

// 导出组件
export default VideoEditor;
```

#### 后端代码规范

```typescript
// 使用 TypeScript 接口定义类型
interface VideoProcessingOptions {
  quality: 'low' | 'medium' | 'high';
  format: string;
  frameRate: number;
}

// 类命名使用 PascalCase
class VideoController {
  // 方法命名使用 camelCase
  async generateVideo(req: Request, res: Response): Promise<void> {
    try {
      // 实现逻辑
    } catch (error) {
      logger.error('Video generation failed:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
```

### 5. 测试要求

#### 单元测试

```typescript
// 为新功能编写单元测试
describe('VideoController', () => {
  let controller: VideoController;
  
  beforeEach(() => {
    controller = new VideoController();
  });
  
  test('should generate video successfully', async () => {
    const mockRequest = createMockRequest();
    const mockResponse = createMockResponse();
    
    await controller.generateVideo(mockRequest, mockResponse);
    
    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({ taskId: expect.any(String) })
    );
  });
});
```

#### 集成测试

```typescript
// 测试 API 端点
describe('POST /api/generateVideo', () => {
  test('should return task ID', async () => {
    const response = await request(app)
      .post('/api/generateVideo')
      .send(mockCanvasState)
      .expect(200);
    
    expect(response.body).toHaveProperty('taskId');
  });
});
```

### 6. 文档更新

如果您的更改涉及：
- 新的 API 端点
- 配置选项变更
- 新功能添加
- 使用方式改变

请同时更新相关文档：
- API 文档
- 用户指南
- 开发指南
- README 文件

## 提交 Pull Request

### 1. 准备提交

```bash
# 确保代码符合规范
npm run lint
npm run type-check

# 运行测试
npm test

# 构建项目
npm run build
```

### 2. 推送分支

```bash
git push origin feature/your-feature-name
```

### 3. 创建 Pull Request

1. 访问 GitHub 仓库页面
2. 点击 "New Pull Request"
3. 选择您的分支
4. 填写 PR 模板

#### PR 标题格式

```
<type>: <description>

例如：
feat: add video timeline drag and drop functionality
fix: resolve audio sync issue in video export
docs: update API documentation for new endpoints
```

#### PR 描述模板

```markdown
## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重构
- [ ] 文档更新
- [ ] 性能优化

## 变更描述
简要描述您的更改内容和原因。

## 测试
- [ ] 添加了新的测试用例
- [ ] 所有现有测试通过
- [ ] 手动测试通过

## 截图/演示
如果适用，请添加截图或 GIF 演示。

## 相关 Issue
Closes #123
Fixes #456

## 检查清单
- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 变更向后兼容
```

### 4. 代码审查

提交 PR 后：
1. 自动化测试将运行
2. 维护者将审查您的代码
3. 根据反馈进行修改
4. 审查通过后合并

## 报告 Bug

### 1. 搜索现有 Issue

在创建新 Issue 之前，请搜索是否已有相关问题。

### 2. Bug 报告模板

```markdown
## Bug 描述
清晰简洁地描述 bug。

## 复现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

## 期望行为
描述您期望发生的情况。

## 实际行为
描述实际发生的情况。

## 截图
如果适用，添加截图帮助解释问题。

## 环境信息
- 操作系统: [例如 macOS 12.0]
- 浏览器: [例如 Chrome 95.0]
- Node.js 版本: [例如 18.12.0]
- 项目版本: [例如 v1.0.0]

## 附加信息
添加任何其他相关信息。
```

## 功能建议

### 1. 功能请求模板

```markdown
## 功能描述
清晰简洁地描述您想要的功能。

## 问题背景
描述这个功能要解决的问题。

## 解决方案
描述您希望的解决方案。

## 替代方案
描述您考虑过的其他解决方案。

## 附加信息
添加任何其他相关信息或截图。
```

## 开发环境设置

### 1. 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../server
npm install
```

### 2. 环境变量配置

```bash
# 复制环境变量模板
cp frontend/.env.example frontend/.env
cp server/.env.example server/.env

# 编辑环境变量
# 添加必要的 API 密钥
```

### 3. 启动开发服务器

```bash
# 启动后端（终端 1）
cd server
npm run dev

# 启动前端（终端 2）
cd frontend
npm start
```

## 代码审查指南

### 作为审查者

1. **功能性**: 代码是否按预期工作？
2. **可读性**: 代码是否清晰易懂？
3. **性能**: 是否有性能问题？
4. **安全性**: 是否存在安全隐患？
5. **测试**: 是否有足够的测试覆盖？

### 作为被审查者

1. **响应及时**: 及时回应审查意见
2. **解释清楚**: 说明设计决策的原因
3. **接受建议**: 开放地接受改进建议
4. **保持礼貌**: 保持专业和友好的态度

## 发布流程

### 1. 版本号规范

我们使用 [语义化版本](https://semver.org/)：
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 2. 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 变更日志已更新
- [ ] 版本号已更新
- [ ] 构建成功

## 社区准则

### 1. 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 2. 沟通方式

- **GitHub Issues**: 报告 bug 和功能请求
- **Pull Requests**: 代码贡献和讨论
- **Discussions**: 一般性讨论和问答

## 获得帮助

如果您在贡献过程中遇到问题：

1. 查看现有文档和 FAQ
2. 搜索相关 Issues
3. 创建新的 Issue 寻求帮助
4. 在 Discussions 中提问

## 致谢

感谢所有为项目做出贡献的开发者！您的贡献让这个项目变得更好。

---

再次感谢您的贡献！🎉
