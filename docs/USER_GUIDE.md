# 用户使用指南

## 概述

Fabric 视频编辑器是一个功能强大的基于 Web 的视频编辑工具，提供直观的拖拽式编辑界面和专业的视频制作功能。本指南将帮助您快速上手并掌握各项功能。

## 快速开始

### 1. 访问应用

打开浏览器，访问 `http://localhost:3000`（开发环境）或您的部署地址。

### 2. 界面概览

应用界面主要分为以下几个区域：

```
┌─────────────────────────────────────────────────────────┐
│                    顶部菜单栏                            │
├─────────────┬─────────────────────────┬─────────────────┤
│             │                         │                 │
│   左侧      │        画布区域          │    右侧控制     │
│   资源库    │                         │      面板       │
│             │                         │                 │
├─────────────┴─────────────────────────┴─────────────────┤
│                    底部时间轴                            │
└─────────────────────────────────────────────────────────┘
```

- **顶部菜单栏**: 项目操作、导出、设置等
- **左侧资源库**: 视频、图片、音频、文本等素材
- **画布区域**: 主要编辑区域，实时预览效果
- **右侧控制面板**: 选中元素的属性调整
- **底部时间轴**: 时间管理和元素排列

## 基础操作

### 1. 创建新项目

1. 点击顶部菜单的"新建项目"
2. 设置画布尺寸（默认 1280x720）
3. 选择背景颜色或渐变
4. 点击"创建"开始编辑

### 2. 添加媒体元素

#### 添加视频
1. 点击左侧"视频"标签
2. 选择本地视频或在线视频库
3. 拖拽视频到画布或时间轴
4. 调整位置和大小

#### 添加图片
1. 点击左侧"图片"标签
2. 从 Pexels 或 Pixabay 搜索图片
3. 点击图片添加到项目
4. 在画布中调整位置

#### 添加音频
1. 点击左侧"音频"标签
2. 上传本地音频或从 Jamendo 选择
3. 音频会自动添加到音频轨道
4. 调整音频的开始时间和持续时间

#### 添加文本
1. 点击左侧"文本"标签
2. 选择文本样式模板
3. 双击画布中的文本进行编辑
4. 在右侧面板调整字体、颜色、大小等

### 3. 元素操作

#### 选择和移动
- **选择**: 点击画布中的元素
- **移动**: 拖拽元素到新位置
- **多选**: 按住 Ctrl/Cmd 点击多个元素

#### 调整大小和旋转
- **调整大小**: 拖拽元素四角的控制点
- **等比缩放**: 按住 Shift 拖拽控制点
- **旋转**: 拖拽元素上方的旋转控制点

#### 层级管理
- **置于顶层**: 右键菜单 → "置于顶层"
- **置于底层**: 右键菜单 → "置于底层"
- **上移一层**: 右键菜单 → "上移一层"
- **下移一层**: 右键菜单 → "下移一层"

## 时间轴操作

### 1. 时间轴界面

时间轴显示所有元素的时间安排：
- **视频轨道**: 显示视频元素
- **音频轨道**: 显示音频元素
- **文本轨道**: 显示文本元素
- **时间指示器**: 红色竖线表示当前播放位置

### 2. 调整元素时间

#### 修改开始时间
1. 在时间轴中选择元素
2. 拖拽元素左边缘调整开始时间
3. 或在右侧面板输入精确时间

#### 修改持续时间
1. 拖拽元素右边缘调整结束时间
2. 或在右侧面板设置持续时间

#### 分割元素
1. 将时间指示器移动到分割点
2. 选择要分割的元素
3. 右键菜单 → "分割"或按 S 键

### 3. 播放控制

- **播放/暂停**: 点击播放按钮或按空格键
- **跳转**: 点击时间轴任意位置
- **逐帧播放**: 使用左右箭头键
- **回到开始**: 按 Home 键
- **跳到结束**: 按 End 键

## 高级功能

### 1. 动画效果

#### 添加动画
1. 选择要添加动画的元素
2. 在右侧面板点击"动画"标签
3. 选择动画类型：
   - **淡入淡出**: 透明度变化
   - **滑动**: 从指定方向滑入/滑出
   - **缩放**: 大小变化动画
   - **旋转**: 旋转动画

#### 调整动画参数
- **持续时间**: 动画播放时长
- **延迟**: 动画开始前的等待时间
- **缓动**: 动画的速度曲线
- **方向**: 滑动动画的方向

### 2. 视觉效果

#### 滤镜效果
1. 选择图片或视频元素
2. 在右侧面板点击"效果"标签
3. 选择滤镜：
   - **模糊**: 高斯模糊效果
   - **亮度**: 调整明暗
   - **对比度**: 调整对比度
   - **饱和度**: 调整色彩饱和度

#### 边框和阴影
1. 在右侧面板点击"样式"标签
2. 设置边框：
   - **颜色**: 边框颜色
   - **宽度**: 边框粗细
   - **样式**: 实线、虚线等
3. 设置阴影：
   - **颜色**: 阴影颜色
   - **偏移**: 阴影位置
   - **模糊**: 阴影模糊程度

### 3. 字幕功能

#### 手动添加字幕
1. 点击底部"字幕"标签
2. 点击"添加字幕"按钮
3. 设置字幕时间和文本内容
4. 调整字幕样式和位置

#### 自动生成字幕
1. 确保项目中有音频或视频
2. 点击"自动生成字幕"
3. 选择语言（支持多种语言）
4. 等待 AI 转录完成
5. 手动校对和调整字幕

### 4. 音频处理

#### 音频调整
1. 选择音频元素
2. 在右侧面板调整：
   - **音量**: 调整音频大小
   - **淡入淡出**: 设置音频渐变
   - **静音**: 临时关闭音频

#### 多轨音频
- 可以添加多个音频轨道
- 支持背景音乐和音效叠加
- 自动音频混合和平衡

## 导出和分享

### 1. 视频导出

#### 基础导出
1. 点击顶部"导出"按钮
2. 选择视频格式（MP4、WebM 等）
3. 设置质量（高、中、低）
4. 点击"开始导出"

#### 高级设置
- **分辨率**: 自定义输出分辨率
- **帧率**: 设置视频帧率（24/30/60 fps）
- **编码器**: 选择视频编码器
- **比特率**: 控制文件大小和质量

### 2. 导出进度

导出过程中可以：
- 查看实时进度
- 预估剩余时间
- 取消导出任务
- 在后台继续编辑

### 3. 项目保存

#### 自动保存
- 项目会自动保存到浏览器本地存储
- 每次操作后延迟保存，避免频繁保存

#### 手动保存
1. 点击顶部"文件"菜单
2. 选择"保存项目"
3. 项目以 JSON 格式保存

#### 导入项目
1. 点击"文件" → "打开项目"
2. 选择之前保存的项目文件
3. 项目会完整恢复所有元素和设置

## 快捷键

### 基础操作
- **Ctrl/Cmd + Z**: 撤销
- **Ctrl/Cmd + Y**: 重做
- **Ctrl/Cmd + C**: 复制
- **Ctrl/Cmd + V**: 粘贴
- **Delete**: 删除选中元素

### 播放控制
- **空格**: 播放/暂停
- **Home**: 跳到开始
- **End**: 跳到结束
- **←/→**: 逐帧移动
- **Shift + ←/→**: 快速移动

### 编辑操作
- **S**: 分割元素
- **G**: 组合元素
- **Ctrl/Cmd + G**: 取消组合
- **Ctrl/Cmd + D**: 复制元素

### 视图控制
- **Ctrl/Cmd + +**: 放大画布
- **Ctrl/Cmd + -**: 缩小画布
- **Ctrl/Cmd + 0**: 适合窗口
- **F**: 全屏预览

## 性能优化建议

### 1. 项目优化
- 避免同时使用过多高分辨率素材
- 合理安排元素层级，减少重叠
- 定期清理不使用的素材

### 2. 浏览器优化
- 使用 Chrome 或 Firefox 最新版本
- 关闭不必要的浏览器标签页
- 确保有足够的内存空间

### 3. 硬件建议
- **CPU**: 4 核心或以上
- **内存**: 8GB 或以上
- **显卡**: 支持硬件加速
- **网络**: 稳定的网络连接

## 常见问题

### 1. 视频无法播放
- 检查视频格式是否支持
- 确认浏览器支持该编解码器
- 尝试转换视频格式

### 2. 导出失败
- 检查网络连接
- 确认服务器状态正常
- 减少项目复杂度重试

### 3. 性能卡顿
- 降低画布缩放比例
- 减少同时显示的元素数量
- 关闭其他占用资源的应用

### 4. 字幕不准确
- 确保音频清晰
- 手动校对自动生成的字幕
- 调整字幕时间同步

## 技巧和窍门

### 1. 高效编辑
- 使用快捷键提高效率
- 善用复制粘贴功能
- 利用网格对齐功能

### 2. 创意技巧
- 尝试不同的动画组合
- 使用多层叠加创造特效
- 合理运用音效增强氛围

### 3. 质量控制
- 预览时检查所有元素
- 注意音频和视频同步
- 导出前进行完整播放测试

通过本指南，您应该能够熟练使用 Fabric 视频编辑器创建专业的视频作品。如有其他问题，请参考故障排除指南或联系技术支持。
