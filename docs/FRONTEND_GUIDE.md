# 前端开发指南

## 概述

本指南详细介绍了 Fabric 视频编辑器前端的架构、开发流程和最佳实践。

## 技术栈

### 核心技术
- **React 18.3.1**: 用户界面框架
- **TypeScript 5.7.3**: 类型安全的 JavaScript
- **MobX 6.13.6**: 响应式状态管理
- **Fabric.js 5.3.1**: 画布操作和图形处理

### UI 和动画
- **Material-UI (MUI) 6.4.3**: UI 组件库
- **Anime.js 3.2.2**: 动画引擎
- **Framer Motion 12.7.4**: React 动画库
- **React Color**: 颜色选择器

### 工具和库
- **@dnd-kit**: 拖拽功能
- **Axios 1.7.9**: HTTP 客户端
- **React Router 6.27.0**: 路由管理
- **Lodash**: 工具函数库

## 项目结构

```
frontend/src/
├── components/          # 通用组件
│   ├── common/         # 基础组件
│   └── LoadingOverlay.tsx
├── editor/             # 编辑器核心组件
│   ├── components/     # 编辑器子组件
│   ├── control-item/   # 控制面板项
│   ├── menu-item/      # 菜单项组件
│   ├── timeline/       # 时间轴组件
│   ├── CanvasContainer.tsx
│   ├── Editor.tsx      # 主编辑器
│   └── ...
├── store/              # 状态管理
│   ├── Store.ts        # 主状态管理器
│   ├── AnimationManager.ts
│   ├── ElementManager.ts
│   └── ...
├── services/           # API 服务
│   ├── audioService.ts
│   ├── imageService.ts
│   └── videoService.ts
├── utils/              # 工具函数
├── types.ts            # 类型定义
└── App.tsx             # 应用入口
```

## 状态管理架构

### MobX Store 结构

```typescript
class Store {
  // 画布相关
  canvas: fabric.Canvas | null;
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor: string;
  
  // 编辑器状态
  editorElements: EditorElement[];
  selectedElement: EditorElement | null;
  editMode: "move" | "hand";
  
  // 时间轴
  currentKeyFrame: number;
  maxTime: number;
  playing: boolean;
  
  // 管理器
  animationManager: AnimationManager;
  elementManager: ElementManager;
  captionManager: CaptionManager;
  historyManager: HistoryManager;
  trackManager: TrackManager;
}
```

### 管理器职责

1. **ElementManager**: 元素生命周期管理
2. **AnimationManager**: 动画系统管理
3. **CaptionManager**: 字幕管理
4. **HistoryManager**: 撤销/重做功能
5. **TrackManager**: 轨道系统管理

## 核心组件开发

### 1. 创建新的编辑器组件

```typescript
// components/MyComponent.tsx
import React from 'react';
import { observer } from 'mobx-react';
import { useStore } from '../store';

interface MyComponentProps {
  // 定义属性
}

const MyComponent: React.FC<MyComponentProps> = observer(({ }) => {
  const store = useStore();
  
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
});

export default MyComponent;
```

### 2. 添加新的元素类型

```typescript
// 1. 在 types.ts 中定义类型
export interface MyElementProperties {
  customProperty: string;
  // 其他属性
}

// 2. 在 ElementManager 中添加处理逻辑
class ElementManager {
  addMyElement(properties: MyElementProperties) {
    const element: EditorElement = {
      id: getUid(),
      type: 'myElement',
      name: 'My Element',
      properties,
      timeFrame: { start: 0, end: 5000 },
      placement: { /* 默认位置 */ }
    };
    
    this.store.addEditorElement(element);
  }
}

// 3. 在 refreshElement 中添加渲染逻辑
refreshElement(element: EditorElement) {
  if (element.type === 'myElement') {
    // 渲染逻辑
  }
}
```

### 3. 创建菜单项组件

```typescript
// editor/menu-item/MyMenuItem.tsx
import React from 'react';
import { observer } from 'mobx-react';
import { Box, Button } from '@mui/material';
import { useStore } from '../../store';

const MyMenuItem: React.FC = observer(() => {
  const store = useStore();
  
  const handleAddElement = () => {
    store.elementManager.addMyElement({
      customProperty: 'default value'
    });
  };
  
  return (
    <Box>
      <Button onClick={handleAddElement}>
        添加我的元素
      </Button>
    </Box>
  );
});

export default MyMenuItem;
```

## API 集成

### 1. 创建服务文件

```typescript
// services/myService.ts
import axios from 'axios';

export interface MyApiResponse {
  data: any[];
  total: number;
}

export const getMyData = async (
  query?: string,
  page: number = 1,
  limit: number = 20
): Promise<MyApiResponse> => {
  try {
    const response = await axios.get('/api/my-endpoint', {
      params: { query, page, limit }
    });
    
    return {
      data: response.data.results,
      total: response.data.total
    };
  } catch (error) {
    console.error('API 调用失败:', error);
    throw error;
  }
};
```

### 2. 在组件中使用服务

```typescript
const MyComponent: React.FC = observer(() => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  
  const loadData = async () => {
    setLoading(true);
    try {
      const result = await getMyData();
      setData(result.data);
    } catch (error) {
      // 错误处理
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    loadData();
  }, []);
  
  return (
    // 组件渲染
  );
});
```

## 动画系统

### 1. 添加新的动画类型

```typescript
// 在 AnimationManager 中添加
class AnimationManager {
  private createMyAnimation(animation: Animation): anime.AnimeParams {
    return {
      targets: `#${animation.targetId}`,
      duration: animation.duration,
      easing: 'easeInOutQuad',
      // 自定义动画属性
      myProperty: [0, 100],
      update: (anim) => {
        // 更新回调
      }
    };
  }
}
```

### 2. 动画与 FFmpeg 同步

```typescript
// 在 Store.ts 中映射动画类型
private mapAnimationTypeToXfadeTransition(animationType: string): string {
  switch (animationType) {
    case 'myAnimation':
      return 'fade'; // 映射到 FFmpeg 滤镜
    default:
      return 'none';
  }
}
```

## 时间轴开发

### 1. 添加新的轨道类型

```typescript
// 在 TrackManager 中
class TrackManager {
  addMyTrack() {
    const track: Track = {
      id: getUid(),
      type: 'myTrack',
      name: 'My Track',
      elements: [],
      visible: true,
      locked: false
    };
    
    this.tracks.push(track);
  }
}
```

### 2. 时间轴组件扩展

```typescript
// timeline/MyTrackComponent.tsx
const MyTrackComponent: React.FC<{ track: Track }> = observer(({ track }) => {
  return (
    <div className="track-container">
      {/* 轨道内容 */}
    </div>
  );
});
```

## 性能优化

### 1. 组件优化

```typescript
// 使用 React.memo 和 observer
const MyComponent = React.memo(observer(({ prop }) => {
  // 组件逻辑
}));

// 避免在渲染中创建新对象
const MyComponent = observer(() => {
  const memoizedValue = useMemo(() => {
    return expensiveCalculation();
  }, [dependency]);
  
  return <div>{memoizedValue}</div>;
});
```

### 2. MobX 优化

```typescript
// 使用 computed 值
class Store {
  @computed
  get visibleElements() {
    return this.editorElements.filter(el => 
      el.timeFrame.start <= this.currentTimeInMs &&
      el.timeFrame.end >= this.currentTimeInMs
    );
  }
}

// 使用 action 包装状态修改
@action
updateElement(id: string, updates: Partial<EditorElement>) {
  const element = this.editorElements.find(el => el.id === id);
  if (element) {
    Object.assign(element, updates);
  }
}
```

### 3. 画布优化

```typescript
// 批量更新画布
updateCanvas() {
  this.canvas.requestRenderAll(); // 而不是 renderAll()
}

// 使用对象池
class ObjectPool {
  private pool: fabric.Object[] = [];
  
  get(): fabric.Object {
    return this.pool.pop() || new fabric.Object();
  }
  
  release(obj: fabric.Object) {
    this.pool.push(obj);
  }
}
```

## 测试

### 1. 单元测试

```typescript
// __tests__/Store.test.ts
import { Store } from '../store/Store';

describe('Store', () => {
  let store: Store;
  
  beforeEach(() => {
    store = new Store();
  });
  
  test('should add element', () => {
    const element = createTestElement();
    store.addEditorElement(element);
    
    expect(store.editorElements).toContain(element);
  });
});
```

### 2. 集成测试

```typescript
// __tests__/integration/Editor.test.tsx
import { render, fireEvent } from '@testing-library/react';
import Editor from '../editor/Editor';

test('should add text element', () => {
  const { getByText, getByTestId } = render(<Editor />);
  
  fireEvent.click(getByText('添加文本'));
  
  expect(getByTestId('text-element')).toBeInTheDocument();
});
```

## 调试技巧

### 1. MobX 调试

```typescript
// 启用 MobX 调试
import { configure } from 'mobx';

configure({
  enforceActions: 'always',
  computedRequiresReaction: true,
  reactionRequiresObservable: true,
  observableRequiresReaction: true,
  disableErrorBoundaries: true
});
```

### 2. 性能监控

```typescript
// 性能监控
const PerformanceMonitor = observer(() => {
  const renderCount = useRef(0);
  renderCount.current++;
  
  console.log(`Component rendered ${renderCount.current} times`);
  
  return null;
});
```

## 最佳实践

### 1. 代码组织
- 按功能模块组织代码
- 使用 TypeScript 严格模式
- 保持组件单一职责
- 使用一致的命名约定

### 2. 状态管理
- 避免深层嵌套状态
- 使用 computed 值减少重复计算
- 合理使用 action 包装状态修改
- 避免在组件中直接修改 store

### 3. 性能
- 使用 React.memo 和 observer
- 避免不必要的重渲染
- 合理使用 useMemo 和 useCallback
- 监控组件渲染次数

### 4. 错误处理
- 使用 Error Boundary
- 实现全局错误处理
- 提供用户友好的错误信息
- 记录详细的错误日志

## 常见问题

### 1. 画布不更新
```typescript
// 确保调用 renderAll
this.canvas.renderAll();

// 或使用 requestRenderAll 进行批量更新
this.canvas.requestRenderAll();
```

### 2. 状态不响应
```typescript
// 确保组件使用 observer
const MyComponent = observer(() => {
  // 组件逻辑
});

// 确保状态修改在 action 中
@action
updateState() {
  this.someProperty = newValue;
}
```

### 3. 内存泄漏
```typescript
// 清理事件监听器
useEffect(() => {
  const handler = () => {};
  window.addEventListener('resize', handler);
  
  return () => {
    window.removeEventListener('resize', handler);
  };
}, []);

// 清理定时器
useEffect(() => {
  const timer = setInterval(() => {}, 1000);
  
  return () => {
    clearInterval(timer);
  };
}, []);
```

## 部署前检查

1. **类型检查**: `npm run type-check`
2. **代码检查**: `npm run lint`
3. **测试**: `npm run test`
4. **构建**: `npm run build`
5. **性能测试**: 使用 React DevTools Profiler

## 相关资源

- [React 官方文档](https://reactjs.org/)
- [MobX 官方文档](https://mobx.js.org/)
- [Fabric.js 官方文档](http://fabricjs.com/)
- [Material-UI 文档](https://mui.com/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
