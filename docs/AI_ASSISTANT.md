# AI助手功能文档

## 概述

AI助手功能集成了Amazon Bedrock Claude Sonnet，为用户提供智能的视频编辑建议和帮助。用户可以通过对话界面与AI交互，获得基于当前项目状态的个性化建议。

## 功能特性

- 🤖 基于Amazon Bedrock Claude Sonnet的智能对话
- 📊 实时分析当前画布状态和元素
- 💡 提供个性化的视频编辑建议
- 🔄 支持上下文感知的对话
- ⚡ 快速响应和错误处理
- 🛡️ 完善的安全验证和频率限制

## 配置要求

### 环境变量

在服务器端需要配置以下环境变量：

```bash
# AWS Bedrock配置
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
```

### AWS权限

确保AWS凭证具有以下权限：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "bedrock:InvokeModel"
      ],
      "Resource": [
        "arn:aws:bedrock:*::foundation-model/anthropic.claude-3-sonnet-20240229-v1:0"
      ]
    }
  ]
}
```

## API接口

### POST /api/ai/chat

发送AI对话请求。

**请求体：**
```json
{
  "userInput": "用户输入的问题或请求",
  "canvasState": {
    "width": 1280,
    "height": 720,
    "backgroundColor": "#000000",
    "elements": [...],
    "animations": [...],
    "captions": [...],
    "tracks": [...]
  }
}
```

**响应：**
```json
{
  "response": "AI的回复内容",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "metadata": {
    "elementCount": 5,
    "canvasSize": "1280x720",
    "processingTime": 1500
  }
}
```

**错误响应：**
```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 前端使用

AI助手通过导航栏的AI按钮访问，点击后会打开对话窗口。

### 主要功能

1. **智能分析**：AI会自动分析当前项目状态
2. **建议提供**：基于项目内容提供编辑建议
3. **问题解答**：回答视频编辑相关问题
4. **快捷操作**：提供常用操作的快捷入口

### 使用示例

- "帮我分析一下当前项目"
- "如何让我的视频更有吸引力？"
- "这个布局有什么问题吗？"
- "建议添加什么元素？"

## 错误处理

系统提供完善的错误处理机制：

### 错误类型

- `INVALID_INPUT`：输入参数无效
- `MISSING_CANVAS_STATE`：缺少画布状态
- `AWS_CREDENTIALS_ERROR`：AWS凭证错误
- `BEDROCK_API_ERROR`：Bedrock API调用失败
- `INTERNAL_ERROR`：内部服务器错误
- `SERVICE_UNAVAILABLE`：服务不可用

### 降级策略

当AWS Bedrock不可用时，系统会自动切换到模拟响应模式，确保功能的基本可用性。

## 安全特性

### 输入验证

- 用户输入长度限制（最大2000字符）
- 画布状态格式验证
- 参数类型检查

### 频率限制

- 每分钟最多10个请求
- 基于IP地址的限制
- 可配置的限制策略

### 数据保护

- 不存储用户对话内容
- 画布状态仅用于当前请求
- 敏感信息过滤

## 监控和日志

### 日志记录

系统记录以下信息：

- 请求和响应时间
- 错误详情和堆栈跟踪
- AWS API调用状态
- 用户输入摘要（不包含敏感信息）

### 性能监控

- 响应时间统计
- 成功率监控
- 错误率分析
- 资源使用情况

## 故障排除

### 常见问题

1. **AWS凭证错误**
   - 检查环境变量设置
   - 验证AWS权限配置
   - 确认区域设置正确

2. **响应缓慢**
   - 检查网络连接
   - 验证AWS服务状态
   - 查看服务器资源使用

3. **功能不可用**
   - 查看服务器日志
   - 检查API端点状态
   - 验证前端配置

### 调试模式

开发环境下可以启用详细日志：

```bash
NODE_ENV=development npm start
```

## 未来扩展

计划中的功能改进：

- 支持更多AI模型
- 增强的上下文理解
- 多语言支持
- 语音交互功能
- 自定义提示词模板
