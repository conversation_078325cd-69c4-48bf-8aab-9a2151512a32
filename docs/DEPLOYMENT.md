# 部署指南

## 概述

本指南详细介绍了如何在不同环境中部署 Fabric 视频编辑器，包括开发环境、测试环境和生产环境。

## 系统要求

### 最低要求
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4+ 核心
- **内存**: 8GB+ RAM
- **存储**: 50GB+ SSD
- **GPU**: 支持硬件加速（可选）

### 软件依赖
- **Node.js**: 18.x 或更高版本
- **npm**: 8.x 或更高版本
- **FFmpeg**: 4.4 或更高版本
- **ImageMagick**: 7.x（可选）

## 环境配置

### 1. 安装 Node.js

#### Ubuntu/Debian
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### CentOS/RHEL
```bash
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

#### macOS
```bash
brew install node@18
```

#### Windows
下载并安装 [Node.js 官方安装包](https://nodejs.org/)

### 2. 安装 FFmpeg

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

#### CentOS/RHEL
```bash
sudo yum install epel-release
sudo yum install ffmpeg
```

#### macOS
```bash
brew install ffmpeg
```

#### Windows
1. 下载 [FFmpeg 预编译版本](https://ffmpeg.org/download.html)
2. 解压到 `C:\ffmpeg`
3. 将 `C:\ffmpeg\bin` 添加到系统 PATH

### 3. 验证安装

```bash
node --version    # 应显示 v18.x.x 或更高
npm --version     # 应显示 8.x.x 或更高
ffmpeg -version   # 应显示 FFmpeg 版本信息
```

## 项目部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd online_video
```

### 2. 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../server
npm install
```

### 3. 环境变量配置

#### 前端环境变量 (.env)
```bash
# frontend/.env
REACT_APP_JAMENDO_API_KEY=your_jamendo_api_key
REACT_APP_PEXELS_API_KEY=your_pexels_api_key
REACT_APP_PIXABAY_API_KEY=your_pixabay_api_key
REACT_APP_API_BASE_URL=http://localhost:8080
```

#### 后端环境变量 (.env)
```bash
# server/.env
PORT=8080
NODE_ENV=production
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
LOG_LEVEL=info
MAX_CONCURRENT_TASKS=5
TEMP_DIR=/tmp/video-editor
OUTPUT_DIR=./output
```

## 开发环境部署

### 1. 启动开发服务器

```bash
# 终端 1: 启动后端
cd server
npm run dev

# 终端 2: 启动前端
cd frontend
npm start
```

### 2. 访问应用

- **前端**: http://localhost:3000
- **后端 API**: http://localhost:8080

## 生产环境部署

### 1. 构建项目

```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd ../server
npm run build
```

### 2. 使用 PM2 部署

#### 安装 PM2
```bash
npm install -g pm2
```

#### 创建 PM2 配置文件
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'video-editor-server',
      script: './dist/index.js',
      cwd: './server',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 8080
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true
    }
  ]
};
```

#### 启动服务
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 3. Nginx 配置

#### 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置文件
```nginx
# /etc/nginx/sites-available/video-editor
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/build;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 增加超时时间（用于长时间的视频处理）
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
}
```

#### 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/video-editor /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Docker 部署

### 1. 前端 Dockerfile

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 后端 Dockerfile

```dockerfile
# server/Dockerfile
FROM node:18-alpine

# 安装 FFmpeg
RUN apk add --no-cache ffmpeg

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建项目
RUN npm run build

# 创建必要的目录
RUN mkdir -p /app/output /app/logs

EXPOSE 8080

CMD ["npm", "start"]
```

### 3. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8080

  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    volumes:
      - ./server/output:/app/output
      - ./server/logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
```

### 4. 启动 Docker 服务

```bash
docker-compose up -d
```

## 云平台部署

### 1. AWS 部署

#### 使用 AWS Elastic Beanstalk

```bash
# 安装 EB CLI
pip install awsebcli

# 初始化项目
eb init

# 创建环境
eb create production

# 部署
eb deploy
```

#### 配置文件
```yaml
# .ebextensions/01_packages.config
packages:
  yum:
    ffmpeg: []
```

### 2. Google Cloud Platform

```bash
# 安装 gcloud CLI
curl https://sdk.cloud.google.com | bash

# 初始化
gcloud init

# 部署到 App Engine
gcloud app deploy
```

#### app.yaml
```yaml
runtime: nodejs18

env_variables:
  NODE_ENV: production
  PORT: 8080

automatic_scaling:
  min_instances: 1
  max_instances: 10
```

### 3. Heroku 部署

```bash
# 安装 Heroku CLI
npm install -g heroku

# 登录
heroku login

# 创建应用
heroku create your-app-name

# 添加 FFmpeg buildpack
heroku buildpacks:add --index 1 https://github.com/jonathanong/heroku-buildpack-ffmpeg-latest.git

# 部署
git push heroku main
```

## 监控和日志

### 1. 日志配置

```javascript
// server/src/utils/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

### 2. 健康检查

```javascript
// server/src/routes/health.ts
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version
  });
});
```

### 3. 性能监控

```bash
# 安装监控工具
npm install --save @sentry/node
npm install --save newrelic
```

## 安全配置

### 1. HTTPS 配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 2. 防火墙配置

```bash
# Ubuntu UFW
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. 安全头配置

```javascript
// server/src/index.ts
import helmet from 'helmet';

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      mediaSrc: ["'self'", "blob:", "https:"]
    }
  }
}));
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/video-editor"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份输出文件
tar -czf $BACKUP_DIR/output_$DATE.tar.gz ./server/output

# 备份日志
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz ./server/logs

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 2. 自动备份

```bash
# 添加到 crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

## 故障排除

### 1. 常见问题

#### FFmpeg 未找到
```bash
# 检查 FFmpeg 安装
which ffmpeg
ffmpeg -version

# 如果未安装，重新安装
sudo apt install ffmpeg
```

#### 端口被占用
```bash
# 查找占用端口的进程
sudo lsof -i :8080

# 杀死进程
sudo kill -9 <PID>
```

#### 内存不足
```bash
# 检查内存使用
free -h
top

# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 2. 性能优化

```bash
# 调整 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 启用 gzip 压缩
# 在 Nginx 配置中添加
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## 维护

### 1. 定期更新

```bash
# 更新依赖
npm update

# 检查安全漏洞
npm audit
npm audit fix
```

### 2. 清理任务

```bash
# 清理临时文件
find /tmp -name "video-editor-*" -mtime +1 -delete

# 清理旧日志
find ./logs -name "*.log" -mtime +7 -delete
```

### 3. 监控脚本

```bash
#!/bin/bash
# monitor.sh

# 检查服务状态
if ! curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "Service is down, restarting..."
    pm2 restart video-editor-server
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is high: $DISK_USAGE%"
    # 清理临时文件
    find /tmp -name "video-editor-*" -mtime +1 -delete
fi
```

## 扩展部署

### 1. 负载均衡

```nginx
upstream backend {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

### 2. 微服务架构

```yaml
# docker-compose.microservices.yml
version: '3.8'

services:
  video-processor:
    build: ./services/video-processor
    replicas: 3
    
  transcription-service:
    build: ./services/transcription
    replicas: 2
    
  api-gateway:
    build: ./services/api-gateway
    ports:
      - "8080:8080"
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，包括监控、安全、备份等重要方面。根据具体需求选择合适的部署方案。
