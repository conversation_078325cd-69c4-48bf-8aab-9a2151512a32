# 故障排除指南

## 概述

本指南提供了 Fabric 视频编辑器常见问题的解决方案，包括安装问题、运行时错误、性能问题等。

## 安装和配置问题

### 1. Node.js 版本不兼容

**问题**: 使用了不兼容的 Node.js 版本

**症状**:
```
Error: The engine "node" is incompatible with this module
```

**解决方案**:
```bash
# 检查当前版本
node --version

# 安装正确版本 (18.x 或更高)
nvm install 18
nvm use 18

# 或使用 n 版本管理器
npm install -g n
n 18
```

### 2. FFmpeg 未安装或路径问题

**问题**: FFmpeg 未正确安装或不在系统 PATH 中

**症状**:
```
Error: FFmpeg not found
spawn ffmpeg ENOENT
```

**解决方案**:

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg

# 验证安装
ffmpeg -version
which ffmpeg
```

#### macOS
```bash
brew install ffmpeg

# 如果 Homebrew 未安装
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Windows
1. 下载 FFmpeg: https://ffmpeg.org/download.html
2. 解压到 `C:\ffmpeg`
3. 添加 `C:\ffmpeg\bin` 到系统 PATH
4. 重启命令提示符验证: `ffmpeg -version`

### 3. 依赖安装失败

**问题**: npm install 失败

**症状**:
```
npm ERR! peer dep missing
npm ERR! network timeout
```

**解决方案**:
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 如果仍然失败，尝试使用 yarn
npm install -g yarn
yarn install

# 或使用不同的镜像源
npm install --registry https://registry.npmmirror.com
```

## 运行时错误

### 1. 端口被占用

**问题**: 默认端口 3000 或 8080 被占用

**症状**:
```
Error: listen EADDRINUSE :::3000
Error: listen EADDRINUSE :::8080
```

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :3000
lsof -i :8080

# 杀死进程
kill -9 <PID>

# 或使用不同端口
PORT=3001 npm start  # 前端
PORT=8081 npm run dev  # 后端
```

### 2. CORS 错误

**问题**: 跨域请求被阻止

**症状**:
```
Access to fetch at 'http://localhost:8080/api/...' from origin 'http://localhost:3000' has been blocked by CORS policy
```

**解决方案**:

#### 检查后端 CORS 配置
```javascript
// server/src/index.ts
import cors from 'cors';

app.use(cors({
  origin: ['http://localhost:3000', 'https://yourdomain.com'],
  credentials: true
}));
```

#### 前端代理配置
```json
// frontend/package.json
{
  "proxy": "http://localhost:8080"
}
```

### 3. 内存不足错误

**问题**: Node.js 内存溢出

**症状**:
```
FATAL ERROR: Ineffective mark-compacts near heap limit
JavaScript heap out of memory
```

**解决方案**:
```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"

# 或在 package.json 中设置
{
  "scripts": {
    "start": "node --max-old-space-size=4096 dist/index.js"
  }
}

# 检查内存使用
node -e "console.log(process.memoryUsage())"
```

## 视频处理问题

### 1. 视频生成失败

**问题**: FFmpeg 命令执行失败

**症状**:
```
FFmpeg process exited with code 1
Error: Invalid argument
```

**解决方案**:

#### 检查 FFmpeg 日志
```javascript
// 在 VideoController 中启用详细日志
const ffmpeg = spawn('ffmpeg', args, {
  stdio: ['pipe', 'pipe', 'pipe']
});

ffmpeg.stderr.on('data', (data) => {
  console.log('FFmpeg stderr:', data.toString());
});
```

#### 常见 FFmpeg 错误
```bash
# 检查输入文件格式
ffprobe input.mp4

# 测试简单的 FFmpeg 命令
ffmpeg -i input.mp4 -t 10 output.mp4

# 检查编解码器支持
ffmpeg -codecs | grep h264
```

### 2. 音频同步问题

**问题**: 音频和视频不同步

**解决方案**:
```javascript
// 确保音频和视频使用相同的时间基准
const audioFilter = `[0:a]atrim=start=${startTime}:duration=${duration},asetpts=PTS-STARTPTS[audio]`;
const videoFilter = `[0:v]trim=start=${startTime}:duration=${duration},setpts=PTS-STARTPTS[video]`;
```

### 3. 大文件处理超时

**问题**: 处理大视频文件时超时

**解决方案**:
```javascript
// 增加超时时间
const timeout = setTimeout(() => {
  ffmpeg.kill('SIGKILL');
}, 300000); // 5分钟

// 使用流式处理
ffmpeg.stdout.on('data', (data) => {
  // 处理输出流
});
```

## 前端问题

### 1. 画布不显示

**问题**: Fabric.js 画布无法渲染

**症状**: 画布区域空白或元素不显示

**解决方案**:
```javascript
// 确保画布容器有正确的尺寸
const container = document.getElementById('canvas-container');
console.log('Container size:', container.offsetWidth, container.offsetHeight);

// 强制重新渲染
canvas.setDimensions({
  width: container.offsetWidth,
  height: container.offsetHeight
});
canvas.renderAll();

// 检查元素是否在可视区域内
const element = canvas.getActiveObject();
console.log('Element position:', element.left, element.top);
```

### 2. 状态不更新

**问题**: MobX 状态变化但组件不重新渲染

**解决方案**:
```javascript
// 确保组件使用 observer
import { observer } from 'mobx-react';

const MyComponent = observer(() => {
  // 组件逻辑
});

// 确保状态修改在 action 中
import { action } from 'mobx';

@action
updateState() {
  this.someProperty = newValue;
}

// 检查 MobX 配置
import { configure } from 'mobx';

configure({
  enforceActions: 'always',
  computedRequiresReaction: true,
  reactionRequiresObservable: true
});
```

### 3. 时间轴拖拽卡顿

**问题**: 拖拽时间轴元素时性能差

**解决方案**:
```javascript
// 使用防抖优化
import { debounce } from 'lodash';

const debouncedUpdate = debounce((element, timeFrame) => {
  store.updateEditorElementTimeFrame(element, timeFrame);
}, 16); // 60fps

// 在拖拽过程中只更新必要的状态
updateEditorElementTimeFrame(element, timeFrame, isDragEnd) {
  if (!isDragEnd) {
    // 拖拽过程中只更新轻量级状态
    this.updateMediaElementsLite(element.id);
  } else {
    // 拖拽结束时完整更新
    this.refreshElements();
  }
}
```

## 性能问题

### 1. 应用启动慢

**问题**: 应用加载时间过长

**解决方案**:
```javascript
// 使用代码分割
import { lazy, Suspense } from 'react';

const Editor = lazy(() => import('./editor/Editor'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <Editor />
    </Suspense>
  );
}

// 优化 bundle 大小
// webpack.config.js
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
};
```

### 2. 内存泄漏

**问题**: 长时间使用后内存持续增长

**解决方案**:
```javascript
// 清理事件监听器
useEffect(() => {
  const handleResize = () => {
    // 处理逻辑
  };
  
  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);

// 清理定时器
useEffect(() => {
  const timer = setInterval(() => {
    // 定时任务
  }, 1000);
  
  return () => {
    clearInterval(timer);
  };
}, []);

// 清理 Fabric.js 对象
const cleanup = () => {
  canvas.getObjects().forEach(obj => {
    canvas.remove(obj);
  });
  canvas.dispose();
};
```

### 3. 大量元素渲染慢

**问题**: 时间轴有大量元素时渲染缓慢

**解决方案**:
```javascript
// 使用虚拟化列表
import { FixedSizeList as List } from 'react-window';

const VirtualizedTimeline = ({ elements }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      <TimelineElement element={elements[index]} />
    </div>
  );
  
  return (
    <List
      height={400}
      itemCount={elements.length}
      itemSize={50}
    >
      {Row}
    </List>
  );
};

// 使用 React.memo 优化组件
const TimelineElement = React.memo(({ element }) => {
  // 组件逻辑
}, (prevProps, nextProps) => {
  return prevProps.element.id === nextProps.element.id &&
         prevProps.element.timeFrame.start === nextProps.element.timeFrame.start;
});
```

## 网络问题

### 1. API 请求超时

**问题**: 长时间的视频处理请求超时

**解决方案**:
```javascript
// 增加超时时间
const response = await fetch('/api/generateVideo', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data),
  timeout: 300000 // 5分钟
});

// 使用轮询代替长连接
const pollProgress = async (taskId) => {
  const poll = async () => {
    try {
      const response = await fetch(`/api/progress/${taskId}`);
      const progress = await response.json();
      
      if (progress.status === 'completed') {
        return progress;
      } else if (progress.status === 'failed') {
        throw new Error(progress.error);
      } else {
        setTimeout(poll, 1000);
      }
    } catch (error) {
      console.error('轮询失败:', error);
      setTimeout(poll, 5000); // 错误时延长间隔
    }
  };
  
  poll();
};
```

### 2. 文件上传失败

**问题**: 大文件上传失败

**解决方案**:
```javascript
// 分块上传
const uploadFile = async (file) => {
  const chunkSize = 1024 * 1024; // 1MB
  const chunks = Math.ceil(file.size / chunkSize);
  
  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);
    
    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('chunkIndex', i);
    formData.append('totalChunks', chunks);
    
    await fetch('/api/upload-chunk', {
      method: 'POST',
      body: formData
    });
  }
};

// 增加服务器文件大小限制
// server/src/index.ts
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ limit: '100mb', extended: true }));
```

## 调试技巧

### 1. 启用详细日志

```javascript
// 前端调试
localStorage.setItem('debug', 'video-editor:*');

// 后端调试
process.env.DEBUG = 'video-editor:*';
process.env.LOG_LEVEL = 'debug';
```

### 2. 性能分析

```javascript
// React DevTools Profiler
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Component:', id, 'Phase:', phase, 'Duration:', actualDuration);
}

<Profiler id="Editor" onRender={onRenderCallback}>
  <Editor />
</Profiler>

// 内存使用监控
setInterval(() => {
  console.log('Memory usage:', process.memoryUsage());
}, 10000);
```

### 3. 错误边界

```javascript
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // 发送错误报告到监控服务
  }
  
  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    
    return this.props.children;
  }
}
```

## 常用诊断命令

### 系统诊断
```bash
# 检查系统资源
top
htop
free -h
df -h

# 检查网络连接
netstat -tulpn
ss -tulpn

# 检查进程
ps aux | grep node
pgrep -f "video-editor"
```

### 应用诊断
```bash
# 检查 Node.js 进程
node -e "console.log(process.versions)"
node -e "console.log(process.memoryUsage())"

# 检查 npm 配置
npm config list
npm doctor

# 检查端口占用
lsof -i :3000
lsof -i :8080
```

### 日志分析
```bash
# 查看应用日志
tail -f server/logs/combined.log
tail -f server/logs/error.log

# 查看系统日志
journalctl -u nginx
journalctl -f

# 分析错误模式
grep "ERROR" server/logs/combined.log | tail -20
```

## 获取帮助

### 1. 收集诊断信息

在报告问题时，请提供以下信息：

```bash
# 系统信息
uname -a
node --version
npm --version
ffmpeg -version

# 错误日志
tail -50 server/logs/error.log

# 网络配置
curl -I http://localhost:8080/health

# 进程状态
ps aux | grep node
```

### 2. 创建最小复现示例

```javascript
// 创建简化的测试用例
const testCase = {
  width: 1280,
  height: 720,
  elements: [
    {
      type: 'text',
      id: 'test-text',
      properties: { text: 'Hello World' },
      timeFrame: { start: 0, end: 5000 }
    }
  ]
};

// 测试 API 调用
fetch('/api/generateVideo', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testCase)
});
```

### 3. 社区资源

- **GitHub Issues**: 报告 bug 和功能请求
- **文档**: 查看最新的 API 文档
- **示例代码**: 参考项目中的测试文件
- **Stack Overflow**: 搜索相关问题和解决方案

记住：在寻求帮助时，详细描述问题、提供错误信息和系统环境，这将大大提高获得有效帮助的可能性。
