{"enabled": true, "name": "Code Quality Analyzer", "description": "Analyzes modified code for potential improvements, including code smells, design patterns, and best practices to enhance readability, maintainability, and performance.", "version": "1", "when": {"type": "fileEdited", "patterns": ["frontend/src/**/*.{ts", "tsx", "js", "jsx}", "server/src/**/*.{ts", "js}"]}, "then": {"type": "askAgent", "prompt": "Analyze the following code changes and provide specific suggestions for improvement. Focus on:\n\n1. Code smells (duplicated code, long methods, complex conditionals)\n2. Design pattern opportunities\n3. Performance optimizations\n4. TypeScript best practices\n5. React best practices (for frontend files)\n6. State management improvements (for MobX code)\n7. Readability enhancements\n8. Error handling improvements\n\nFor each suggestion:\n- Explain the issue clearly\n- Provide a specific code example showing the improvement\n- Explain the benefits of the change\n\nKeep suggestions practical and maintainable within the existing architecture. Prioritize changes that would have the most significant impact on code quality."}}