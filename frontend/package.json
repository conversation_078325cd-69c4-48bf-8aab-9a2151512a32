{"name": "fabric-video-editor", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start-server": "ts-node src/server/index.ts"}, "browser": {"fs": false, "path": false, "os": false}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@types/fabric": "^5.3.10", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "19.1.6", "@types/react-router-dom": "^5.3.3", "ajv": "^8.12.0", "animejs": "^3.2.2", "autoprefixer": "10.4.21", "axios": "^1.10.0", "fabric": "^5.3.1", "framer-motion": "^12.23.6", "gifuct-js": "^2.1.2", "install": "^0.13.0", "lodash": "^4.17.21", "mobx": "^6.13.7", "mobx-react": "^9.2.0", "npm": "^10.8.3", "react": "19.1.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-dom": "19.1.0", "react-hotkeys-hook": "^5.1.0", "react-hover-video-player": "^10.0.2", "react-icons": "^5.5.0", "react-map-interaction": "^2.1.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.26", "react-virtuoso": "^4.13.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "store": "^2.0.12", "typescript": "5.8.3", "wavesurfer.js": "^7.10.0"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@types/animejs": "^3.1.13", "@types/lodash": "^4.17.20", "@types/react-color": "^3.0.13", "eslint": "9.31.0", "eslint-config-react-app": "^7.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}