The app's live link to test in the browser is: https://fabric-video-editor.vercel.app/

Do you need a custom editor? Get in touch with me at [Linked In](https://www.linkedin.com/in/amit-digga/)
Other: [Website](https://www.amitdigga.dev/) | [Twitter](https://twitter.com/AmitDigga) |

This was a hobby project. I will add support for other features in the future. Looking for backend/ffmpeg developers to help me generate video from Canvas in the backend.

# Fabric Video Editor

Fabric Video Editor is a video editor that runs in the browser. It is built with fabric.js, React, Tailwindcss, Mobx, typescript, Material-UI, and anime.js.

## Samples

[Samples section remains unchanged]

## Tech Explanation

todo

## Features

- [x] User can add
  - [x] Text
  - [x] Images
  - [x] Video
  - [x] Audio
- [x] User can change
  - [x] Canvas Background Color (including gradients)
- [x] Timeline
- [x] Export Video with Audio
- [x] Animations
- [x] Filters
- [x] Element alignment and ordering controls
- [x] Undo/Redo functionality
- [x] Project export/import

## Main Issues

1. There might be a problem with audio handling
2. Exported video doesn't have a time duration
3. Exported videos have flickering issue

## Future Features

3. Properties Editing panel
4. Video Trimming

## Getting Started

1. Clone the repo

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

[The rest of the README remains unchanged]