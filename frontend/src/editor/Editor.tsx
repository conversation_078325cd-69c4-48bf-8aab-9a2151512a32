import { Box } from "@mui/material";

import { observer } from "mobx-react-lite";
import { Navbar } from "./Navbar";
import MenuList from "./MenuLists";
import { MenuItem } from "./menu-item";
import ControlList from "./ControlLists";
import { ControlItem } from "./control-item";

import { TimeLine } from "./timeline/TimeLinePanel";
import { CanvasContainer } from "./CanvasContainer";

const Editor = () => {
  return (
    <Box
      sx={{
        height: "100%",
        width: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Navbar />

      <Box sx={{ flex: 1, position: "relative", overflow: "hidden" }}>
        <MenuList />
        <MenuItem />
        <ControlList />
        <ControlItem />
        <CanvasContainer />
      </Box>

      <TimeLine />
    </Box>
  );
};

export default Editor;
