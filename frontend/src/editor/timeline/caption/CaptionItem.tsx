import { Box, Tooltip } from "@mui/material";
import { observer } from "mobx-react";
import React, { useCallback, useContext, useMemo, useState } from "react";
import { StoreContext } from "../../../store";
import CaptionContextMenu from "./CaptionContextMenu";
import { CaptionItemProps } from "./types/types";
import { CAPTION_COLOR, captionItemStyles, handleStyles } from "../styles";
import { CaptionContent } from "./CaptionContent";
import { DurationLabel } from "./DurationLabel";
import { SnapIndicator } from "./SnapIndicator";
import { useCaptionDrag } from "./hooks/useCaptionDrag";
import {
  calculateTimelinePosition,
  timeStringToMs,
  getTimelineContainerWidth,
} from "../../../utils/timeUtils";
import { useLanguage } from "../../../i18n/LanguageContext";

/**
 * CaptionItem组件 - 显示时间线上的字幕项
 * 支持拖拽调整开始和结束时间，以及整体移动
 */
const CaptionItemComponent = observer(
  ({
    caption,
    containerWidth,
    handleTimeFrameChange,
    allCaptions,
  }: CaptionItemProps) => {
    const store = useContext(StoreContext);
    const { t } = useLanguage();
    const [isHovering, setIsHovering] = useState(false);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);
    const itemId = `caption-item-${caption.id}`;

    const startTimeMs = useMemo(
      () => timeStringToMs(caption.startTime),
      [caption.startTime]
    );
    const endTimeMs = useMemo(
      () => timeStringToMs(caption.endTime),
      [caption.endTime]
    );
    const isSelected = caption.isSelected === true;

    // 使用自定义hook处理拖拽逻辑
    const {
      handleLeftHandleDrag,
      handleRightHandleDrag,
      handleCenterDrag,
      isDragging,
      isSnappedStart,
      isSnappedEnd,
    } = useCaptionDrag({
      caption,
      allCaptions,
      containerWidth,
      store,
    });

    const showControls = (isHovering || isSelected) && !isDragging;

    // 计算位置样式 - 优化依赖项
    const positionStyles = useMemo(() => {
      // 如果没有提供容器宽度，使用统一的容器宽度获取函数
      const currentContainerWidth =
        containerWidth || getTimelineContainerWidth();

      // 如果仍然没有容器宽度，返回默认值
      if (!currentContainerWidth) {
        return {
          width: "0%",
          left: "0%",
        };
      }

      const width =
        ((endTimeMs - startTimeMs) / store.timelineDisplayDuration) * 100;

      const left = calculateTimelinePosition(
        startTimeMs,
        store.timelineDisplayDuration,
        store.timelinePan.offsetX,
        currentContainerWidth
      );

      return {
        width: `${width}%`,
        left: `${left}%`,
      };
    }, [
      startTimeMs,
      endTimeMs,
      store.timelineDisplayDuration,
      store.timelinePan.offsetX,
      containerWidth,
    ]);

    // 鼠标事件处理 - 使用稳定的回调
    const handleMouseEnter = useCallback(() => setIsHovering(true), []);
    const handleMouseLeave = useCallback(() => setIsHovering(false), []);

    // 点击事件处理 - 使用稳定的回调
    const handleCaptionClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.selectCaption(caption.id);
      },
      [store, caption.id]
    );

    // 双击事件处理 - 使用稳定的回调
    const handleCaptionDoubleClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.handleSeek(startTimeMs);
      },
      [store, startTimeMs]
    );

    // 右键菜单处理
    const handleContextMenu = useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();

        // 选中字幕
        store.selectCaption(caption.id);

        // 显示右键菜单
        setContextMenu({
          mouseX: e.clientX,
          mouseY: e.clientY,
        });
      },
      [store, caption.id]
    );

    // 关闭右键菜单
    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    // 优化样式对象的创建
    const itemStyles = useMemo(
      () => ({
        ...captionItemStyles(isSelected, isHovering, isDragging),
        ...positionStyles,
        willChange: "left, width",
      }),
      [isSelected, isHovering, isDragging, positionStyles]
    );

    return (
      <Box
        id={itemId}
        key={caption.id}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        sx={itemStyles}
        onMouseDown={handleCenterDrag}
        onContextMenu={handleContextMenu}
      >
        {/* 右键菜单 */}
        <CaptionContextMenu
          caption={caption}
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
        <CaptionContent
          caption={caption}
          handleClick={handleCaptionClick}
          handleDoubleClick={handleCaptionDoubleClick}
        />

        <DurationLabel
          startTime={caption.startTime}
          endTime={caption.endTime}
          isVisible={isHovering || isSelected}
        />

        {/* 吸附指示器 */}
        <SnapIndicator position="left" isVisible={isSnappedStart} />
        <SnapIndicator position="right" isVisible={isSnappedEnd} />

        {showControls && (
          <>
            <Tooltip
              title={
                <Box sx={{ fontWeight: "bold" }}>
                  {t("timeline_start")}: {caption.startTime}
                </Box>
              }
              arrow
              placement="top"
              enterDelay={500}
              leaveDelay={200}
            >
              <Box
                id="left-handle"
                sx={{
                  ...handleStyles,
                  left: 0,
                  transform: "translate(0%, -50%)",
                  borderColor: isSnappedStart ? "#ff9800" : CAPTION_COLOR,
                  boxShadow: isSnappedStart
                    ? "0 0 8px rgba(255,152,0,0.5)"
                    : "none",
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  handleLeftHandleDrag(e);
                }}
              />
            </Tooltip>

            <Tooltip
              title={
                <Box sx={{ fontWeight: "bold" }}>
                  {t("timeline_end")}: {caption.endTime}
                </Box>
              }
              arrow
              placement="top"
              enterDelay={500}
              leaveDelay={200}
            >
              <Box
                id="right-handle"
                sx={{
                  ...handleStyles,
                  right: 0,
                  transform: "translate(0%, -50%)",
                  borderColor: isSnappedEnd ? "#ff9800" : CAPTION_COLOR,
                  boxShadow: isSnappedEnd
                    ? "0 0 8px rgba(255,152,0,0.5)"
                    : "none",
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  handleRightHandleDrag(e);
                }}
              />
            </Tooltip>
          </>
        )}
      </Box>
    );
  }
);

// 使用React.memo包装组件，并提供自定义比较函数
export const CaptionItem = React.memo(
  CaptionItemComponent,
  (prevProps, nextProps) => {
    // 比较关键的props，避免不必要的重渲染
    return (
      prevProps.caption.id === nextProps.caption.id &&
      prevProps.caption.startTime === nextProps.caption.startTime &&
      prevProps.caption.endTime === nextProps.caption.endTime &&
      prevProps.caption.text === nextProps.caption.text &&
      prevProps.caption.isSelected === nextProps.caption.isSelected &&
      prevProps.containerWidth === nextProps.containerWidth &&
      prevProps.allCaptions.length === nextProps.allCaptions.length
    );
  }
);

// 添加displayName以便于调试
(CaptionItem as any).displayName = "CaptionItem";
