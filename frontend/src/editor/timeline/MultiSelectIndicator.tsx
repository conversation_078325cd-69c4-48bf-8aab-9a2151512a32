"use client";
import { Box, Typography, IconButton, Toolt<PERSON> } from "@mui/material";
import { observer } from "mobx-react";
import { useContext } from "react";
import { StoreContext } from "../../store";
import { SelectAll, Delete } from "@mui/icons-material";

/**
 * 多选状态指示器组件
 * 显示当前选中元素的数量和提供清除选择的功能
 */
export const MultiSelectIndicator = observer(() => {
  const store = useContext(StoreContext);

  // 如果没有多选元素，不显示指示器
  if (store.selectedElements.length === 0) {
    return null;
  }

  const handleClearSelection = () => {
    store.clearAllSelections();
  };

  return (
    <Box
      sx={{
        position: "fixed",
        bottom: 24,
        right: 24,
        zIndex: 1000,
        display: "flex",
        alignItems: "center",
        gap: 1,
        bgcolor: "grey.100",
        color: "text.secondary",
        px: 2,
        py: 1,
        borderRadius: 2,
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        backdropFilter: "blur(8px)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
        // 添加一些动画效果
        animation: "slideInUp 0.3s ease-out",
        "@keyframes slideInUp": {
          "0%": {
            transform: "translateY(100%)",
            opacity: 0,
          },
          "100%": {
            transform: "translateY(0)",
            opacity: 1,
          },
        },
      }}
    >
      <SelectAll sx={{ fontSize: 20 }} />
      <Typography variant="body2" fontWeight="medium">
        Selected {store.selectedElements.length} elements
      </Typography>
      <Tooltip title="Clear selection" placement="top">
        <IconButton
          onClick={handleClearSelection}
          size="small"
          sx={{
            color: "text.secondary",
            bgcolor: "rgba(0, 0, 0, 0.04)",
            "&:hover": {
              bgcolor: "rgba(0, 0, 0, 0.08)",
              color: "text.primary",
            },
            ml: 0.5,
          }}
        >
          <Delete fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  );
});

// 设置组件显示名称
(MultiSelectIndicator as any).displayName = "MultiSelectIndicator";
