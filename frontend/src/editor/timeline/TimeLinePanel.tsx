"use client";
import React, {
  useContext,
  useMemo,
  useCallback,
  useRef,
  useState,
  useEffect,
  createContext,
} from "react";
import { SeekPlayer } from "./SeekPlayer";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import { Box, styled, useTheme } from "@mui/material";
import { TimeLineListByTrack } from "./TimeLineListByTrack";
import { TimeScaleMarkers } from "./TimeScaleMarkers";
import { MultiSelectIndicator } from "./MultiSelectIndicator";
import { getUid } from "../../utils";

import {
  TIMELINE_CONSTANTS,
  calculateTimelinePosition,
  getTimelineContainerWidth,
} from "../../utils/timeUtils";

// 图片拖拽上下文
interface ImageDragContextType {
  dragTargetTrack: string | null;
  dragTargetGap: string | null;
  isDragOver: boolean;
}

const ImageDragContext = createContext<ImageDragContextType>({
  dragTargetTrack: null,
  dragTargetGap: null,
  isDragOver: false,
});

export const useImageDrag = () => useContext(ImageDragContext);

export const TimeLine = observer(() => {
  const store = useContext(StoreContext);
  const theme = useTheme();
  const timelineContainerRef = useRef<HTMLDivElement | null>(null);
  const indicatorRef = useRef<HTMLDivElement | null>(null);
  const scrollbarTrackRef = useRef<HTMLDivElement | null>(null);
  const scrollbarThumbRef = useRef<HTMLDivElement | null>(null);
  const [isIndicatorDragging, setIsIndicatorDragging] = useState(false);
  const [isScrollbarDragging, setIsScrollbarDragging] = useState(false);
  const [initialMouseX, setInitialMouseX] = useState(0);
  const [initialTime, setInitialTime] = useState(0);
  const [initialScrollPosition, setInitialScrollPosition] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);

  // 使用状态来存储和更新容器宽度
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 使用ResizeObserver监听容器宽度变化
  useEffect(() => {
    // 初始化时获取一次容器宽度
    setContainerWidth(getTimelineContainerWidth());

    // 创建ResizeObserver监听容器宽度变化
    const observer = new ResizeObserver(() => {
      setContainerWidth(getTimelineContainerWidth());
    });

    // 获取容器元素并开始监听
    const container = document.querySelector(".timeline-container");
    if (container) {
      observer.observe(container);
    }

    // 清理函数
    return () => {
      if (container) {
        observer.unobserve(container);
      }
      observer.disconnect();
    };
  }, []);

  // 使用通用的位置计算函数来计算当前时间指示器的位置
  const adjustedPercentOfCurrentTime = useMemo(() => {
    // 如果容器宽度未知，返回0
    if (!containerWidth) return 0;

    return calculateTimelinePosition(
      store.currentTimeInMs,
      store.timelineDisplayDuration,
      store.timelinePan.offsetX,
      containerWidth // 传递容器宽度参数
    );
  }, [
    store.currentTimeInMs,
    store.timelinePan.offsetX,
    store.timelineDisplayDuration,
    containerWidth, // 添加containerWidth作为依赖项
  ]);

  // Calculate scrollbar position and size
  const scrollbarInfo = useMemo(() => {
    // Calculate the maximum offset allowed using the same logic as in the store
    const maxAllowedTime = Math.max(
      store.timelineDisplayDuration,
      store.maxTime
    );
    const maxOffset = Math.max(
      store.timelineDisplayDuration * 0.5,
      maxAllowedTime - store.timelineDisplayDuration
    );

    // Calculate the total scrollable range (from 0 to maxOffset)
    const totalScrollRange = maxOffset;

    // Calculate the current position as a percentage (0-100)
    // Map from [0, maxOffset] to [0, 100]
    // Positive offsetX means the timeline is panned left (showing later parts)
    const position = (store.timelinePan.offsetX / totalScrollRange) * 100;

    // Calculate thumb size based on visible range relative to total time
    const visibleRangeRatio = store.timelineDisplayDuration / maxAllowedTime;
    // Make sure thumb isn't too small
    const thumbSize = Math.max(10, visibleRangeRatio * 100);

    return { position, thumbSize, maxOffset, totalScrollRange };
  }, [store.timelinePan.offsetX, store.timelineDisplayDuration, store.maxTime]);

  const Indicator = styled(Box)(({ theme }) => ({
    position: "absolute",
    top: 0,
    left: 0,
    width: "2px",
    height: "100%",
    backgroundColor: theme.palette.primary.main,
    cursor: "ew-resize",
    borderRadius: "2px",
    zIndex: 1, // Local z-index within the container
    transition: "width 0.1s ease, background-color 0.1s ease",
    "&:hover": {
      width: "4px",
    },
  }));

  // Handle indicator dragging
  const handleIndicatorMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.button === 0) {
        // Left mouse button
        setIsIndicatorDragging(true);
        setInitialMouseX(e.clientX);
        setInitialTime(store.currentTimeInMs);
        e.stopPropagation(); // Prevent timeline panning
        e.preventDefault(); // Prevent other default behaviors
      }
    },
    [store]
  );

  const handleIndicatorMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isIndicatorDragging || !timelineContainerRef.current) return;

      const containerWidth = timelineContainerRef.current.clientWidth;
      const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

      // 调整有效内容区域宽度（减去左侧偏移）
      const effectiveWidth = containerWidth - HANDLE_WIDTH;

      const deltaX = e.clientX - initialMouseX;
      const deltaTime =
        (deltaX / effectiveWidth) * store.timelineDisplayDuration;

      // 计算新的时间，并限制在0到timelineDisplayDuration之间
      let newTime = Math.max(
        0,
        Math.min(store.timelineDisplayDuration, initialTime + deltaTime)
      );

      // 如果有元素，进一步限制不超过所有元素的最大endtime
      if (store.maxDuration > 0) {
        // 确保不超过最大元素endtime
        newTime = Math.min(newTime, store.maxDuration);
      }

      // Update the current time in the store
      store.handleSeek(newTime);

      e.preventDefault();
    },
    [isIndicatorDragging, initialMouseX, initialTime, store]
  );

  const handleIndicatorMouseUp = useCallback(
    (e: MouseEvent) => {
      if (isIndicatorDragging) {
        setIsIndicatorDragging(false);
        e.preventDefault();
      }
    },
    [isIndicatorDragging]
  );

  // 滚动条的拖拽处理
  const handleScrollbarMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.button === 0 && scrollbarThumbRef.current) {
        // 只处理左键点击
        setIsScrollbarDragging(true);
        setInitialMouseX(e.clientX);
        setInitialScrollPosition(store.timelinePan.offsetX);

        // 防止事件冒泡和默认行为
        e.stopPropagation();
        e.preventDefault();
      }
    },
    [store.timelinePan.offsetX]
  );

  const handleScrollbarTrackClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (
        e.button === 0 &&
        scrollbarTrackRef.current &&
        scrollbarThumbRef.current &&
        !isScrollbarDragging
      ) {
        // 计算点击位置相对于轨道的位置百分比
        const trackRect = scrollbarTrackRef.current.getBoundingClientRect();
        const clickPosition = (e.clientX - trackRect.left) / trackRect.width;

        // 根据点击位置计算新的偏移量
        const newOffset = clickPosition * scrollbarInfo.maxOffset;

        // 更新时间线偏移
        store.setTimelinePanOffset(newOffset);

        e.stopPropagation();
        e.preventDefault();
      }
    },
    [isScrollbarDragging, scrollbarInfo.maxOffset, store]
  );

  const handleScrollbarMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isScrollbarDragging || !scrollbarTrackRef.current) return;

      const trackRect = scrollbarTrackRef.current.getBoundingClientRect();
      const trackWidth = trackRect.width;

      // 计算鼠标移动的距离
      const deltaX = e.clientX - initialMouseX;

      // 将移动距离转换为时间线偏移量的变化
      const deltaRatio = deltaX / trackWidth;
      const deltaOffset = deltaRatio * scrollbarInfo.totalScrollRange;

      // 计算新的偏移量并限制在有效范围内
      const newOffset = Math.max(
        0,
        Math.min(scrollbarInfo.maxOffset, initialScrollPosition + deltaOffset)
      );

      // 更新时间线偏移
      store.setTimelinePanOffset(newOffset);

      e.preventDefault();
    },
    [
      isScrollbarDragging,
      initialMouseX,
      initialScrollPosition,
      scrollbarInfo.maxOffset,
      scrollbarInfo.totalScrollRange,
      store,
    ]
  );

  const handleScrollbarMouseUp = useCallback(() => {
    if (isScrollbarDragging) {
      setIsScrollbarDragging(false);
    }
  }, [isScrollbarDragging]);

  // Add and remove global event listeners for indicator dragging and scrollbar dragging
  useEffect(() => {
    window.addEventListener("mousemove", handleIndicatorMouseMove);
    window.addEventListener("mouseup", handleIndicatorMouseUp);
    window.addEventListener("mousemove", handleScrollbarMouseMove);
    window.addEventListener("mouseup", handleScrollbarMouseUp);

    return () => {
      window.removeEventListener("mousemove", handleIndicatorMouseMove);
      window.removeEventListener("mouseup", handleIndicatorMouseUp);
      window.removeEventListener("mousemove", handleScrollbarMouseMove);
      window.removeEventListener("mouseup", handleScrollbarMouseUp);
    };
  }, [
    handleIndicatorMouseMove,
    handleIndicatorMouseUp,
    handleScrollbarMouseMove,
    handleScrollbarMouseUp,
  ]);

  // Add non-passive wheel event listener to prevent browser back/forward navigation
  useEffect(() => {
    const container = timelineContainerRef.current;
    if (!container) return;

    // 使用 requestAnimationFrame 优化滚动事件处理
    let ticking = false;
    let lastWheelEvent: WheelEvent | null = null;

    const handleTimelineWheel = (e: WheelEvent) => {
      // 检测是否有水平滚动分量或是否按下了Ctrl/Shift键
      const hasHorizontalComponent = Math.abs(e.deltaX) > 0;
      const isCtrlPressed = e.ctrlKey || e.metaKey;
      const isShiftPressed = e.shiftKey;

      // 如果有水平滚动分量或按下了Ctrl/Shift键，阻止默认行为
      if (hasHorizontalComponent || isCtrlPressed || isShiftPressed) {
        e.preventDefault();
      }

      // 保存最新的事件
      lastWheelEvent = e;

      // 如果已经请求了动画帧，不再重复请求
      if (ticking) return;

      // 请求动画帧处理滚动
      ticking = true;
      requestAnimationFrame(() => {
        if (!lastWheelEvent) return;

        // 获取事件相对于容器的位置
        const rect = container.getBoundingClientRect();
        const clientX = lastWheelEvent.clientX - rect.left;

        // 检查是否在有效区域内（排除左侧10px的排序把手区域）
        const isInSortHandleArea = clientX < 10;

        // 如果在排序把手区域内，不处理滚动
        if (!isInSortHandleArea) {
          // 检测是否按下了Ctrl键（Mac上的Command键）
          const isCtrlPressed =
            lastWheelEvent.ctrlKey || lastWheelEvent.metaKey;

          // 如果是Ctrl+滚轮，执行缩放操作
          if (isCtrlPressed) {
            // 根据滚轮方向确定是放大还是缩小
            // deltaY < 0 表示向上滚动，放大；deltaY > 0 表示向下滚动，缩小
            if (lastWheelEvent.deltaY < 0) {
              // 放大时间线（减小显示的时间范围）
              const newDuration = Math.max(
                store.timelineDisplayDuration / 1.1,
                10 * 1000
              ); // 最小显示10秒
              store.setTimelineDisplayDuration(newDuration);
            } else {
              // 缩小时间线（增加显示的时间范围）
              const newDuration = Math.min(
                store.timelineDisplayDuration * 1.1,
                store.maxTime
              ); // 最大显示整个时间线
              store.setTimelineDisplayDuration(newDuration);
            }
          } else {
            // 处理普通滚动（水平滚动或Shift+垂直滚动）
            // 检测是否是水平滚动或Shift+垂直滚动
            const isHorizontalScroll =
              Math.abs(lastWheelEvent.deltaX) >
                Math.abs(lastWheelEvent.deltaY) || lastWheelEvent.shiftKey;

            if (isHorizontalScroll) {
              // 调用store的滚动处理方法
              const delta = lastWheelEvent.shiftKey
                ? lastWheelEvent.deltaY
                : lastWheelEvent.deltaX;
              store.handleTimelineWheel(delta);
            }
            // 对于纯垂直滚动，不需要额外处理，让浏览器自然处理
            // 这样就可以使用原生滚动条
          }
        }

        // 重置标志，允许处理下一个事件
        ticking = false;
        lastWheelEvent = null;
      });
    };

    // 使用原生事件监听器添加wheel事件，明确指定passive: false
    container.addEventListener("wheel", handleTimelineWheel, {
      passive: false,
    });

    return () => {
      container.removeEventListener("wheel", handleTimelineWheel);
    };
  }, [store]);

  const handleMouseLeave = useCallback(() => {
    if (store.timelinePan.isDragging) {
      store.endTimelinePan();
      // Reset cursor
      if (timelineContainerRef.current) {
        timelineContainerRef.current.style.cursor = "grab";
      }
    }
  }, [store]);

  const handleDoubleClick = useCallback(
    (e: React.MouseEvent) => {
      // 获取点击位置相对于容器的横坐标
      if (!timelineContainerRef.current) return;

      const rect = timelineContainerRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;

      // 如果点击在排序把手区域内，不执行重置操作
      if (clickX < 10) return;

      // 清除所有选中状态
      store.clearAllSelections();

      // Reset the timeline pan offset on double click
      store.resetTimelinePan();
    },
    [store]
  );

  // 处理时间线容器的单击事件
  const handleTimelineClick = useCallback(
    (e: React.MouseEvent) => {
      // 检查点击的目标是否是时间线容器本身或其直接子元素
      const target = e.target as HTMLElement;
      const container = timelineContainerRef.current;

      if (!container) return;

      // 如果点击的是时间线元素、轨道元素或其他交互元素，不清除选择
      if (
        target.closest(".timeline-element-container") ||
        target.closest(".timeline-track") ||
        target.closest(".timeline-indicator") ||
        target.closest(".caption-item") ||
        target.closest(".gap-indicator") ||
        target.closest("[data-testid]") ||
        target.closest("button") ||
        target.closest('[role="button"]')
      ) {
        return;
      }

      // 如果点击的是时间线空白区域，清除所有选择
      store.clearAllSelections();
    },
    [store]
  );

  // 状态管理：拖拽目标轨道
  const [dragTargetTrack, setDragTargetTrack] = useState<string | null>(null);
  const [dragTargetGap, setDragTargetGap] = useState<string | null>(null);

  // 检测拖拽目标的辅助函数
  const detectDragTarget = useCallback((e: React.DragEvent) => {
    const target = e.target as HTMLElement;

    // 检查是否拖拽到轨道上
    const trackElement = target.closest(".timeline-track");
    if (trackElement) {
      const trackId = trackElement.getAttribute("data-track-id");
      if (trackId) {
        setDragTargetTrack(trackId);
        setDragTargetGap(null);
        return { type: "track", id: trackId };
      }
    }

    // 检查是否拖拽到轨道间隙上
    const gapElement = target.closest('[id^="gap-"]');
    if (gapElement) {
      const gapId = gapElement.id;
      setDragTargetTrack(null);
      setDragTargetGap(gapId);
      return { type: "gap", id: gapId };
    }

    // 没有检测到特定目标
    setDragTargetTrack(null);
    setDragTargetGap(null);
    return { type: "none", id: null };
  }, []);

  // 处理拖拽进入
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      setIsDragOver(true);

      // 检测拖拽目标
      detectDragTarget(e);
    },
    [detectDragTarget]
  );

  // 处理拖拽离开
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // 只有当拖拽真正离开时间线容器时才设置为false
    if (!timelineContainerRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      setDragTargetTrack(null);
      setDragTargetGap(null);
    }
  }, []);

  // 处理轨道分配的辅助函数
  const handleTrackAssignment = useCallback(
    (element: any, dropTarget: { type: string; id: string | null }) => {
      if (dropTarget.type === "track" && dropTarget.id) {
        // 拖拽到现有轨道上
        const targetTrack = store.trackManager.tracks.find(
          (t: any) => t.id === dropTarget.id
        );
        if (targetTrack) {
          // 检查轨道类型是否匹配
          const isCompatibleTrack =
            (element.type === "audio" && targetTrack.type === "audio") ||
            (["image", "gif", "video", "shape"].includes(element.type) &&
              targetTrack.type === "media") ||
            (element.type === "text" && targetTrack.type === "text");

          if (isCompatibleTrack) {
            // 移动元素到目标轨道，并进行碰撞检测
            const { startTime, endTime, hasOverlap } =
              store.trackManager.checkElementOverlap(element, dropTarget.id);

            // 如果有碰撞，调整元素的时间范围
            if (hasOverlap) {
              console.log(
                `拖拽元素检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
              );

              // 更新元素的时间范围以避免碰撞
              element.timeFrame = {
                start: startTime,
                end: endTime,
              };
            }

            store.trackManager.moveElementToTrack(element.id, dropTarget.id);
          } else {
            // 轨道类型不匹配，创建新轨道
            const trackType = store.trackManager.getTrackTypeFromElementType(
              element.type
            );
            const newTrack = store.trackManager.createTrack(trackType);
            store.trackManager.moveElementToTrack(element.id, newTrack.id);
          }
        }
      } else if (dropTarget.type === "gap" && dropTarget.id) {
        // 拖拽到轨道间隙，根据间隙位置创建新轨道
        const gapId = dropTarget.id;
        let newTrackPosition = 0;

        // 解析间隙ID来确定新轨道的位置
        if (gapId.startsWith("gap-before-")) {
          // 在第一个轨道之前
          newTrackPosition = 0;
        } else if (gapId.startsWith("gap-between-")) {
          // 在两个轨道之间
          const parts = gapId.replace("gap-between-", "").split("-");
          if (parts.length >= 2) {
            const beforeTrackId = parts[0];
            const beforeTrackIndex = store.trackManager.tracks.findIndex(
              (t: any) => t.id === beforeTrackId
            );
            if (beforeTrackIndex !== -1) {
              newTrackPosition = beforeTrackIndex + 1;
            }
          }
        } else if (gapId.startsWith("gap-after-")) {
          // 在最后一个轨道之后
          newTrackPosition = store.trackManager.tracks.length;
        } else if (gapId === "gap-final-bottom") {
          // 在最底部
          newTrackPosition = store.trackManager.tracks.length;
        }

        // 在指定位置创建新轨道
        const trackType = store.trackManager.getTrackTypeFromElementType(
          element.type
        );
        const newTrack = store.trackManager.createTrackAtPosition(
          trackType,
          newTrackPosition
        );
        store.trackManager.moveElementToTrack(element.id, newTrack.id);
      } else {
        // 没有特定拖拽目标，使用默认轨道分配逻辑，并进行碰撞检测
        const trackType = store.trackManager.getTrackTypeFromElementType(
          element.type
        );
        const defaultTrackId = store.trackManager.defaultTracks[trackType];

        if (
          defaultTrackId &&
          store.trackManager.tracks.find((t: any) => t.id === defaultTrackId)
        ) {
          // 有默认轨道且轨道存在，进行碰撞检测后添加到默认轨道
          const { startTime, endTime, hasOverlap } =
            store.trackManager.checkElementOverlap(element, defaultTrackId);

          // 如果有碰撞，调整元素的时间范围
          if (hasOverlap) {
            console.log(
              `拖拽元素到默认轨道检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
            );

            // 更新元素的时间范围以避免碰撞
            element.timeFrame = {
              start: startTime,
              end: endTime,
            };
          }

          store.trackManager.addElementToTrack(defaultTrackId, element.id);
        } else {
          // 没有默认轨道或默认轨道不存在，创建新轨道
          const newTrack = store.trackManager.createTrack(trackType);
          store.trackManager.addElementToTrack(newTrack.id, element.id);
        }
      }

      // 清理空轨道
      store.trackManager.removeEmptyTracks();

      // 更新Canvas顺序
      store.updateCanvasOrderByTrackOrder();
    },
    [store]
  );

  // 计算拖拽放置时间的辅助函数
  const calculateDropTime = useCallback(
    (e: React.DragEvent) => {
      if (!timelineContainerRef.current) return 0;

      const rect = timelineContainerRef.current.getBoundingClientRect();
      const dropX = e.clientX - rect.left;
      const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;

      // 调整有效内容区域宽度（减去左侧偏移）
      const effectiveWidth = rect.width - HANDLE_WIDTH;
      const dropPercent = Math.max(
        0,
        Math.min(1, (dropX - HANDLE_WIDTH) / effectiveWidth)
      );

      // 计算对应的时间位置
      const visibleStartTime = store.timelinePan.offsetX;
      return visibleStartTime + dropPercent * store.timelineDisplayDuration;
    },
    [store]
  );

  // 处理媒体元素拖拽的通用逻辑
  const handleMediaElementDrop = useCallback(
    (dragData: any, dropTime: number, dropTarget: any) => {
      const id = getUid();

      // 根据元素类型设置默认持续时间
      const getDefaultDuration = (type: string) => {
        switch (type) {
          case "video":
            return 5000; // 5秒，稍后会根据实际视频时长更新
          case "audio":
            return 30000; // 30秒，稍后会根据实际音频时长更新
          default:
            return 3000; // 3秒
        }
      };

      const timeFrame = {
        start: dropTime,
        end: dropTime + getDefaultDuration(dragData.type),
      };

      let element: any;

      // 根据类型创建占位符或直接创建元素
      switch (dragData.type) {
        case "image":
          element = store.addImagePlaceholder(
            id,
            dragData.src,
            timeFrame,
            dragData.metadata
          );
          break;
        case "gif":
          element = store.addGifPlaceholder(
            id,
            dragData.src,
            timeFrame,
            dragData.metadata
          );
          break;
        case "video":
          element = store.addVideoPlaceholder(
            id,
            dragData.src,
            timeFrame,
            dragData.metadata
          );
          break;
        case "audio":
          element = store.addAudioPlaceholder(
            id,
            dragData.src,
            timeFrame,
            dragData.metadata
          );
          break;
        case "shape":
          element = store.addShapeElementAtTime(dragData.shapeType, timeFrame);
          break;
        case "text":
          element = store.addTextAtTime(dragData.textData, timeFrame);
          break;
        default:
          console.error("Unknown drag data type:", dragData.type);
          return;
      }

      // 处理轨道分配（包含碰撞检测）
      handleTrackAssignment(element, dropTarget);

      // 选中新添加的元素
      store.setSelectedElement(element);

      // 对于需要异步加载的媒体元素，设置加载逻辑
      if (["image", "gif", "video", "audio"].includes(dragData.type)) {
        setupAsyncMediaLoading(dragData, element, id);
      } else {
        // 对于shape和text，立即更新时长和时间线
        store.updateMaxTime();
        store.updateTimeTo(store.currentTimeInMs);
      }

      // 无论哪种类型，都需要更新最大时间（因为碰撞检测可能改变了时间范围）
      store.updateMaxTime();
    },
    [store, handleTrackAssignment]
  );

  // 处理媒体加载完成后的碰撞检测
  const handleMediaLoadComplete = useCallback(
    (element: any) => {
      // 获取元素所在的轨道
      const track = store.trackManager.getTrackByElementId(element.id);
      if (!track) {
        console.warn("无法找到元素所在的轨道");
        store.updateMaxTime();
        return;
      }

      // 重新检测碰撞（因为媒体时长可能已经改变）
      const { startTime, endTime, hasOverlap } =
        store.trackManager.checkElementOverlap(element, track.id);

      // 如果有碰撞，调整元素的时间范围
      if (hasOverlap) {
        console.log(
          `媒体加载完成后检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
        );

        // 更新元素的时间范围以避免碰撞
        const updatedElement = {
          ...element,
          timeFrame: {
            start: startTime,
            end: endTime,
          },
        };
        store.updateEditorElement(updatedElement);
      }

      // 更新最大时间
      store.updateMaxTime();
    },
    [store]
  );

  // 设置异步媒体加载的辅助函数
  const setupAsyncMediaLoading = useCallback(
    (dragData: any, element: any, id: string) => {
      const createElement = (tagName: string) => {
        const mediaElement = document.createElement(tagName) as any;
        mediaElement.src = dragData.src;
        mediaElement.id = `${dragData.type}-${id}`;
        mediaElement.style.display = "none";
        return mediaElement;
      };

      let mediaElement: any;

      switch (dragData.type) {
        case "image":
          mediaElement = createElement("img");
          mediaElement.onload = () => {
            store.replacePlaceholderWithImage(element, mediaElement);
            // 图片不会改变时长，不需要重新检测碰撞
            store.updateMaxTime();
          };
          break;
        case "gif":
          mediaElement = createElement("img");
          mediaElement.onload = () => {
            store.replacePlaceholderWithGif(element, mediaElement);
            // GIF不会改变时长，不需要重新检测碰撞
            store.updateMaxTime();
          };
          break;
        case "video":
          mediaElement = createElement("video");
          mediaElement.onloadedmetadata = () => {
            store.replacePlaceholderWithVideo(element, mediaElement);
            // 视频可能会改变时长，需要重新检测碰撞
            handleMediaLoadComplete(element);
          };
          break;
        case "audio":
          mediaElement = createElement("audio");
          // 对于Jamendo音频，设置跨域属性
          if (
            dragData.src.includes("/api/proxy/") ||
            dragData.src.includes("jamendo.com") ||
            dragData.src.includes("jamen.do")
          ) {
            mediaElement.crossOrigin = "anonymous";
          }
          mediaElement.onloadedmetadata = () => {
            store.replacePlaceholderWithAudio(element, mediaElement);
            // 音频可能会改变时长，需要重新检测碰撞
            handleMediaLoadComplete(element);
          };
          break;
      }

      if (mediaElement) {
        mediaElement.onerror = () => {
          console.error(`Failed to load dropped ${dragData.type}`);
          store.removeEditorElement(element.id);
        };
      }
    },
    [store, handleMediaLoadComplete]
  );

  // 处理拖拽放置
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        const dragData = JSON.parse(e.dataTransfer.getData("application/json"));

        // 检测拖拽目标
        const dropTarget = detectDragTarget(e);

        // 计算放置时间
        const dropTime = calculateDropTime(e);

        // 处理媒体元素拖拽
        handleMediaElementDrop(dragData, dropTime, dropTarget);
      } catch (error) {
        console.error("Error handling drop:", error);
      } finally {
        // 清理拖拽状态
        setDragTargetTrack(null);
        setDragTargetGap(null);
      }
    },
    [store, detectDragTarget, calculateDropTime, handleMediaElementDrop]
  );

  // 注意：此函数已被原生wheel事件监听器替代
  // 保留此注释作为代码历史记录

  return (
    <Box
      sx={{
        height: "250px",
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center", // Added for vertical centering
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          mx: 1,
          justifyContent: "center",
          alignItems: "center",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <SeekPlayer />
        <Box
          ref={timelineContainerRef}
          className="timeline-container"
          sx={{
            position: "relative",
            overflowX: "hidden", // 隐藏水平滚动条
            overflowY: "auto", // 允许垂直滚动
            width: "100%",
            py: 1, // 减小内边距
            height: "calc(100% - 15px)", // 调整高度，留出更多空间给滚动条
            touchAction: "pan-y",
            WebkitOverflowScrolling: "touch",
            boxSizing: "border-box", // 确保padding和border包含在宽度内
            maxWidth: "100%", // 确保不超过父容器宽度
            display: "flex", // 使用flex布局
            flexDirection: "column", // 垂直方向排列子元素
            // 拖拽状态样式
            backgroundColor: isDragOver
              ? theme.palette.action.hover
              : "transparent",
            transition: "background-color 0.2s ease",
          }}
          onMouseLeave={handleMouseLeave}
          onClick={handleTimelineClick}
          onDoubleClick={handleDoubleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* Indicator positioned on top of everything - only shown when there are elements or captions */}
          {(store.editorElements.length > 0 || store.captions.length > 0) && (
            <Box
              position="relative"
              sx={{
                position: "absolute",
                left: `calc(${adjustedPercentOfCurrentTime}% + 10px)`, // 10px是排序把手宽度
                height: "100%", // Full height
                top: 0,
                zIndex: 50, // Higher than both TimeScaleMarkers and VirtualizedTimeline
                transform: "translateX(-50%)",
                cursor: "ew-resize",
                pointerEvents: "auto", // Ensure it can receive mouse events
              }}
              onMouseDown={handleIndicatorMouseDown}
            >
              <Indicator
                ref={indicatorRef}
                style={{
                  width: isIndicatorDragging ? "3px" : "2px",
                  backgroundColor: isIndicatorDragging
                    ? theme.palette.primary.dark
                    : theme.palette.primary.main,
                  transform: "translateX(-50%)",
                }}
              />
              {/* Indicator handle for better drag UX */}
              <Box
                sx={{
                  position: "absolute",
                  top: "0px", // Changed from "45px" to "0px" to position at the top
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "10px",
                  height: "12px",

                  borderRadius: "0", // Changed from "50%" to "0" to make it square
                  backgroundColor: theme.palette.primary.main,
                  transition:
                    "transform 0.1s ease, box-shadow 0.1s ease, background-color 0.1s ease",
                  boxShadow: isIndicatorDragging
                    ? "0 0 8px rgba(0, 0, 0, 0.5)"
                    : "0 0 4px rgba(0, 0, 0, 0.3)",
                  zIndex: 2, // Ensure handle is above the line
                }}
              />
              {/* 倒三角形指向indicator */}
              <Box
                sx={{
                  position: "absolute",
                  top: "12px", // 就在方形handle下方
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: 0,
                  height: 0,
                  borderLeft: "5px solid transparent",
                  borderRight: "5px solid transparent",
                  borderTop: `6px solid ${theme.palette.primary.main}`,
                  zIndex: 2,
                }}
              />
            </Box>
          )}

          {/* Time scale markers */}
          <TimeScaleMarkers />

          {/* 使用按轨道分组的时间线列表 */}
          <ImageDragContext.Provider
            value={{
              dragTargetTrack,
              dragTargetGap,
              isDragOver,
            }}
          >
            <TimeLineListByTrack />
          </ImageDragContext.Provider>
        </Box>

        {/* 水平滚动条 */}
        <Box
          ref={scrollbarTrackRef}
          onClick={handleScrollbarTrackClick}
          sx={{
            position: "relative",
            width: "calc(100% - 20px)", // 保留与时间线相同宽度
            height: "10px",
            marginLeft: "10px", // 与时间线左侧对齐（排序把手宽度）

            borderRadius: "6px",
            cursor: "pointer",
          }}
        >
          <Box
            ref={scrollbarThumbRef}
            onMouseDown={handleScrollbarMouseDown}
            sx={{
              position: "absolute",
              left: `${scrollbarInfo.position}%`,
              width: "80px", // 固定宽度为80px
              height: "8px",
              top: "2px",
              backgroundColor:
                theme.palette.mode === "dark"
                  ? "rgba(255, 255, 255, 0.4)"
                  : "rgba(0, 0, 0, 0.3)",
              borderRadius: "4px",
              cursor: "grab",
              "&:hover": {
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255, 255, 255, 0.6)"
                    : "rgba(0, 0, 0, 0.4)",
              },
              "&:active": {
                cursor: "grabbing",
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255, 255, 255, 0.7)"
                    : "rgba(0, 0, 0, 0.5)",
              },
              transition: "background-color 0.2s ease",
            }}
          />
        </Box>
      </Box>

      {/* 多选状态指示器 */}
      <MultiSelectIndicator />
    </Box>
  );
});
