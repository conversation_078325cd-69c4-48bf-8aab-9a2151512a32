import React from "react";
import { Box, LinearProgress, Typography } from "@mui/material";
import { alpha } from "@mui/material/styles";

interface LoadingProgressBarProps {
  isLoading: boolean;
  progress: number;
  hasError: boolean;
  elementType: string;
}

export const LoadingProgressBar: React.FC<LoadingProgressBarProps> = ({
  isLoading,
  progress,
  hasError,
  elementType,
}) => {
  if (!isLoading && !hasError) {
    return null;
  }

  return (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: alpha("#000", 0.7),
        zIndex: 10,
        borderRadius: 1,
      }}
    >
      {hasError ? (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: "#ff5252",
              fontSize: "10px",
              fontWeight: "bold",
            }}
          >
            Loading failed
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 0.5,
            width: "80%",
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: "#fff",
              fontSize: "9px",
              fontWeight: "bold",
            }}
          >
            loading...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              width: "100%",
              height: 3,
              borderRadius: 1.5,
              backgroundColor: alpha("#fff", 0.3),
              "& .MuiLinearProgress-bar": {
                backgroundColor: "#4caf50",
                borderRadius: 1.5,
              },
            }}
          />
          {/* <Typography
            variant="caption"
            sx={{
              color: "#fff",
              fontSize: "8px",
            }}
          >
            {Math.round(progress)}%
          </Typography> */}
        </Box>
      )}
    </Box>
  );
};

export default LoadingProgressBar;
