"use client";
import { Box, useTheme } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useContext,
  useLayoutEffect,
  useRef,
  useState,
  useMemo,
} from "react";
import { StoreContext } from "../../store";
import { Caption } from "../../types";
import {
  formatTime,
  getTimelineContainerWidth,
  timeStringToMs,
} from "../../utils/timeUtils";
import { CaptionItem } from "./caption/CaptionItem";
import { CaptionsTrackViewProps } from "./caption/types/types";
import { CAPTION_HEIGHT } from "./styles";
import CaptionTrackContextMenu from "./menu/CaptionTrackContextMenu";

// 组件类型定义
interface CaptionContentProps {
  caption: Caption;
  handleClick: (e: React.MouseEvent) => void;
  handleDoubleClick: (e: React.MouseEvent) => void;
}

// 主字幕轨道视图组件
export const CaptionsTrackView = observer(
  ({ captions }: CaptionsTrackViewProps) => {
    const store = useContext(StoreContext);
    const theme = useTheme();
    const trackRef = useRef<HTMLDivElement>(null);
    const [containerWidth, setContainerWidth] = useState<number | null>(null);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);

    useLayoutEffect(() => {
      const updateWidth = () => {
        // 使用统一的容器宽度获取函数
        setContainerWidth(getTimelineContainerWidth());
      };

      updateWidth();
      window.addEventListener("resize", updateWidth);

      return () => window.removeEventListener("resize", updateWidth);
    }, []);

    // 计算可见的字幕项
    const visibleCaptions = useMemo(() => {
      if (!captions || captions.length === 0 || containerWidth === null) {
        return [];
      }

      const offsetX = store.timelinePan.offsetX; // ms
      const timelineDisplayDuration = store.timelineDisplayDuration; // ms

      // 视口代表的时间范围
      // offsetX 是视口左侧对应的时间点
      // 视口右侧对应的时间点是 offsetX + timelineDisplayDuration
      // 但这里我们需要的是视口在整个时间轴上的"可见"部分，
      // 而 offsetX 已经是相对于0的偏移了。
      // 容器宽度对应的总时间是 timelineDisplayDuration
      // 滚动导致左侧不可见的时间是 offsetX

      // 实际可见范围的开始时间
      const viewStartTimeMs = offsetX;
      // 实际可见范围的结束时间
      const viewEndTimeMs = offsetX + timelineDisplayDuration;

      // 为了更平滑的滚动，可以给视口范围增加一些 buffer
      // 例如，前后各增加视口宽度的一半对应的时间
      const bufferTimeMs = timelineDisplayDuration * 0.2; // 减小缓冲到20%
      const bufferedViewStartTimeMs = Math.max(
        0,
        viewStartTimeMs - bufferTimeMs
      );
      const bufferedViewEndTimeMs = viewEndTimeMs + bufferTimeMs;

      return captions.filter((caption) => {
        const captionStartMs = timeStringToMs(caption.startTime);
        const captionEndMs = timeStringToMs(caption.endTime);
        // 判断字幕时间段与（带缓冲的）视口时间段是否有交集
        return (
          captionStartMs < bufferedViewEndTimeMs &&
          captionEndMs > bufferedViewStartTimeMs
        );
      });
    }, [
      captions,
      containerWidth,
      store.timelinePan.offsetX,
      store.timelineDisplayDuration,
    ]);

    // 右键菜单处理函数
    const handleContextMenu = useCallback((e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // 显示右键菜单
      setContextMenu({
        mouseX: e.clientX,
        mouseY: e.clientY,
      });
    }, []);

    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    if (!captions || captions.length === 0) {
      return (
        <>
          <Box
            ref={trackRef}
            onContextMenu={handleContextMenu}
            sx={{
              position: "relative",
              width: "100%",
              height: CAPTION_HEIGHT,
              my: 1,
              bgcolor: "grey.50",
              borderRadius: 1,
              cursor: "context-menu",
            }}
          />

          <CaptionTrackContextMenu
            contextMenu={contextMenu}
            handleClose={handleCloseContextMenu}
          />
        </>
      );
    }

    const handleTimeFrameChange = useCallback(
      (caption: Caption, start: number, end: number) => {
        const formattedStart = formatTime(start);
        const formattedEnd = formatTime(end);

        const startTimeFormatted = store.formatCaptionTime(formattedStart);
        const endTimeFormatted = store.formatCaptionTime(formattedEnd);

        store.updateCaption(caption.id, "startTime", startTimeFormatted);
        store.updateCaption(caption.id, "endTime", endTimeFormatted);
      },
      [store]
    );

    return (
      <>
        <Box
          ref={trackRef}
          onContextMenu={handleContextMenu}
          sx={{
            position: "relative",
            width: "100%",
            left: "10px",
            height: CAPTION_HEIGHT,
            my: 0.5, // 减小字幕轨道的上下边距
            bgcolor: "grey.100",
            borderRadius: 1,
            px: 0.5,
            overflow: "visible",
            cursor: "context-menu",
          }}
        >
          {visibleCaptions.map((caption) => (
            <CaptionItem
              key={caption.id}
              caption={caption}
              containerWidth={containerWidth}
              handleTimeFrameChange={handleTimeFrameChange}
              allCaptions={captions}
            />
          ))}
        </Box>

        <CaptionTrackContextMenu
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
      </>
    );
  }
);
