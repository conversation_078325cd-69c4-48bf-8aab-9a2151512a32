# 字幕位置与效果修复说明 (最终版 v9 - 前后端坐标系统一)

## 问题描述

之前字幕在 Canvas 中的位置和后台生成的视频中字幕位置存在不一致，主要原因是前后端对字幕 Y 坐标的定义和原点不同。前端 Fabric.js 默认使用文本框顶部作为 `originY`，而后端 ASS 字幕的 `\pos(x,y)` 结合 `\an` 标签可以更灵活地定位，但我们之前的补偿计算不够完美。

## 问题本质

前后端坐标系统和对齐原点（`originY`）的不统一，导致需要复杂的偏移计算来试图弥合差异，但这种计算很难做到完美精确，尤其是在字体、行数变化时。

## 解决方案：统一坐标基准

我们采取了更根本的解决方案：统一前端和后端的坐标基准。

1.  **前端调整 (`frontend/src/store/CaptionManager.ts`)**:

    - 将全局字幕样式 `globalCaptionStyle` 中的 `originY` 默认值从 `'top'` 修改为 `'bottom'`。相应地调整了 `positionY` 的初始值（例如，设为 `-50`，表示字幕在默认底部边距之上 50px）。
    - 创建 `fabric.Textbox` 对象 (`captionTextObject`) 时，其 `originY` 属性现在直接使用 `globalCaptionStyle.originY`（通常为 `'bottom'`）。
    - `captionTop` 的计算逻辑现在基于 `globalCaptionStyle.originY` 来确定是顶部对齐、中心对齐还是底部对齐，并加上 `positionY` 偏移。例如，当 `originY` 为 `'bottom'` 时，`captionTop` 计算的是文本框底部应该在画布上的 Y 坐标。

2.  **后端调整 (`server/src/ffmpeg/utils/assSubtitleUtils.ts`)**:
    - 完全移除了之前用于补偿基线偏移的 `baselineOffset` 计算。
    - 后端计算 `yPos` (用于 ASS 的 `\pos(x,y)`) 的逻辑现在与前端计算 `captionTop` 的逻辑完全一致。它根据从前端传递过来的 `originY` 和 `positionY` 来计算目标 Y 坐标。
    - ASS 字幕的 `\an` 标签（例如 `\an2` 代表底部居中对齐）将确保文本框的相应边缘（由`\an`指定，如底部、顶部或中心）对齐到 `\pos` 标签中指定的 `yPos`。

通过以上修改，前端 Fabric.js 对象和后端生成的 ASS 字幕现在使用相同的坐标定义。例如，如果 `originY` 设置为 `'bottom'`：

- 前端 `fabricObject.top` 将表示文本框的底部在画布上的 Y 坐标。
- 后端 `\pos(x,y)` 中的 `y` (配合如 `\an2`) 也将表示文本框的底部在视频帧上的 Y 坐标。

这样就消除了因坐标系不匹配而引入的复杂性和潜在误差。

### 前端 `CaptionManager.ts` 主要修改点:

**`resetGlobalCaptionStyle`**:

```typescript
this.globalCaptionStyle = {
  // ...其他样式...
  positionX: 0,
  positionY: -50, // 示例值，表示在默认底部之上50px
  originX: "center",
  originY: "bottom", // 改为 'bottom'
};
```

**`updateCurrentCaption` (创建 fabric.Textbox)**:

```typescript
this.captionTextObject = new fabric.Textbox(captionText, {
  // ...其他样式...
  originX: captionStyles.originX || "center",
  originY: captionStyles.originY || "bottom", // 使用全局样式中的 originY
  // ...其他样式...
});
```

**`updateCurrentCaption` (计算 `captionTop`)**:

```typescript
switch (captionStyles.originY) {
  case "top":
    captionTop = 0 + (this.globalCaptionStyle.positionY || 0);
    break;
  case "center":
    captionTop =
      this.store.canvasHeight / 2 + (this.globalCaptionStyle.positionY || 0);
    break;
  case "bottom":
  default:
    const bottomMargin = Math.round(this.store.canvasHeight * 0.02); // 0.02 为ASS_CONFIG.DEFAULT_MARGIN_RATIO
    captionTop =
      this.store.canvasHeight -
      bottomMargin +
      (this.globalCaptionStyle.positionY || 0);
    break;
}
```

### 后端 `assSubtitleUtils.ts` 主要修改点:

**`generateASSContent` (计算 `yPos`)**:

```typescript
let yPos;
const defaultBottomMarginRatio = ASS_CONFIG.DEFAULT_MARGIN_RATIO; // 与前端一致

switch (finalStyle.originY) {
  case "top":
    yPos = 0;
    break;
  case "center":
    yPos = canvasHeight / 2;
    break;
  case "bottom":
  default:
    yPos = canvasHeight - canvasHeight * defaultBottomMarginRatio;
    break;
}
if (finalStyle.positionY !== undefined) {
  yPos += finalStyle.positionY;
}
// 移除了所有 baselineOffset 计算

// alignmentV 的计算逻辑保持不变，用于选择正确的 n 标签
// ...

escapedText = `{\\an${alignmentV}\\pos(${Math.round(xPos)},${Math.round(
  yPos
)})}${escapedText}`;
```

## 如何验证修复

1.  **重新编译** 前端和后端代码。
2.  打开视频编辑器。
3.  添加一个或多个字幕，可以尝试不同的行数和文本内容。
4.  **检查 Canvas 预览**：确保字幕在 Canvas 中显示在你期望的位置（基于新的 `originY: 'bottom'` 和 `positionY` 设置）。
5.  **使用调试工具**：
    - 点击编辑器中的 "调试字幕位置" 按钮。
    - 在浏览器控制台查看日志输出。
    - 关注 `后端计算坐标 (预期)` 和 `前端实际坐标`。它们的 `deltaX` 和 `deltaY` 应该非常小（理想情况小于 1 像素）。
    ```
    后端计算坐标 (预期): {x: ..., y: ...}
    前端实际坐标: {x: ..., y: ...}
    坐标差异: {deltaX: ..., deltaY: ...}
    ✅ 前后端坐标基本一致！
    ```
6.  **检查生成视频**：
    - 生成包含字幕的视频。
    - 仔细对比视频中字幕的位置与 Canvas 预览中的位置。它们现在应该高度一致。

## 阴影与字符间距

之前的阴影效果和字符间距修复（v8 版本及之前）依然有效，本次修改主要集中在位置对齐上。

## 版本历史

- **v1-v4**: 尝试使用基线偏移（`baselineOffset`）在后端调整 Y 坐标。
- **v5**: 后端改用 ASS 的 `\an` 对齐标签，简化了部分逻辑但未完全解决偏移。
- **v6-v8**: 持续调整 `baselineOffset` 系数，并增强了阴影和字符间距的处理。
- **v9**: **采用根本性方案，统一前后端的坐标原点（`originY` 设置为 `'bottom'`）和 Y 坐标计算逻辑，移除了后端复杂的 `baselineOffset` 补偿。**
