# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands for Development

### Frontend Development

```bash
# Install frontend dependencies
cd frontend && npm install

# Start frontend development server (default port: 3000)
cd frontend && npm start

# Build frontend for production
cd frontend && npm run build

# Run frontend tests
cd frontend && npm test
```

### Backend Development

```bash
# Install backend dependencies
cd server && npm install

# Start backend development server with hot reload
cd server && npm run dev

# Build backend for production
cd server && npm run build

# Start backend production server
cd server && npm start

# Setup system fonts for text rendering (required once)
cd server && npm run setup-fonts

# Run backend tests
cd server && npm test
```

### Full Stack Development

To develop the full application:

```bash
# Terminal 1 - Backend
cd server && npm run dev

# Terminal 2 - Frontend
cd frontend && npm start
```

## Code Architecture

### Project Overview

Fabric Video Editor is a web-based video editing application that provides a canvas-based editing experience. Users can create, edit, and export video compositions by combining various media elements including videos, images, audio, text, and shapes.

### Core Architecture

The application follows a client-server architecture:

1. **Frontend**: React application with MobX state management and Fabric.js for canvas operations
2. **Backend**: Node.js server with Express.js and FFmpeg integration for video processing

### Key Technologies

- **Frontend**: React, TypeScript, MobX, Fabric.js, Anime.js, Material-UI
- **Backend**: Node.js, TypeScript, Express.js, FFmpeg, ImageMagick

### Frontend Structure

The frontend follows a well-organized structure:

1. **Store Layer**: MobX stores manage application state
   - `Store.ts`: Central state container
   - `ElementManager.ts`: Handles canvas elements (videos, images, text, etc.)
   - `AnimationManager.ts`: Manages animations and effects
   - `CanvasManager.ts`: Controls canvas rendering and interaction
   - `TimelineManager.ts`: Manages timeline and playback
   
2. **Editor Layer**: Components for the video editor interface
   - `components/`: Reusable UI components
   - `editor/`: Editor-specific components
   - `editor/guide-lines/`: Alignment and centering guidelines
   - `editor/control-item/`: Property panels for different element types
   - `editor/menu-item/`: Left sidebar menu items
   - `editor/timeline/`: Timeline components and drag functionality

3. **Utils Layer**: Helper functions and utilities
   - `fabric-utils.ts`: Fabric.js helper functions
   - `ShapeFactory.ts`: Shape creation utilities
   - `timeUtils.ts`: Time calculation helpers

### Backend Structure

The backend is structured around:

1. **FFmpeg Command Generation**: Creates and executes FFmpeg commands for video processing
   - `ffmpeg/FFmpegCommandGenerator.ts`: Main command builder
   - `ffmpeg/filters/`: Filter generators for different media types

2. **API Endpoints**: Handles requests from the frontend
   - `/api/generateVideo`: Creates videos from canvas state
   - `/api/progress/:taskId`: Provides progress updates

3. **Services**: Business logic for video processing
   - `ProgressTracker.ts`: Tracks video generation progress
   - `ValidationService.ts`: Validates input data

## Key Concepts

### 1. Canvas Elements

Canvas elements are the core building blocks:
- **Videos**: Video clips with timing controls
- **Images**: Static images or animated GIFs
- **Audio**: Sound tracks that play in sync with video
- **Text**: Text elements with styling options
- **Shapes**: Vector shapes with styling options

Elements are managed by `ElementManager.ts` which handles:
- Creating elements of different types
- Setting properties and placement
- Handling user interactions (move, scale, rotate)
- Supporting element alignment with guide lines

### 2. Timeline and Animation

The timeline system:
- Manages playback and seeking
- Controls element visibility based on time
- Supports animation keyframes
- Handles track management for organizing elements

### 3. Export Process

When exporting a video:
1. Canvas state is sent to the backend
2. `FFmpegCommandGenerator` creates a command based on elements
3. FFmpeg processes the command to generate video
4. Progress updates are sent to the frontend
5. Final video is made available for download

## Common Workflows

### Adding New Elements

1. User selects element type from the sidebar
2. Element is created and added to the canvas
3. Element is added to the appropriate track in the timeline
4. Properties panel updates to show element options

### Editing Elements

1. User selects an element on the canvas
2. Property panel shows options specific to that element type
3. Changes update both the canvas display and the element data

### Timeline Operations

1. Elements can be dragged to adjust timing
2. Duration can be modified by dragging element edges
3. Playhead position determines current frame view
4. Split operations can divide elements at the playhead

### Alignment and Positioning

The recent improvements to the codebase have enhanced the alignment guide lines:
- `aligning_guidelines.ts`: Provides guides for aligning elements to each other
- `centering_guidelines.ts`: Helps align elements to the canvas center